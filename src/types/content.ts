export interface ContentSection {
  id: string;
  type: 'hero' | 'text' | 'list' | 'quote' | 'code' | 'cta' | 'divider';
  content: any;
}

export interface HeroSection extends ContentSection {
  type: 'hero';
  content: {
    title: string;
    subtitle: string;
    highlight?: string;
  };
}

export interface TextSection extends ContentSection {
  type: 'text';
  content: {
    heading?: string;
    subheading?: string;
    paragraphs: string[];
    emphasis?: {
      type: 'bold' | 'italic' | 'underline' | 'highlight';
      text: string;
    }[];
  };
}

export interface ListSection extends ContentSection {
  type: 'list';
  content: {
    heading?: string;
    items: {
      title: string;
      description: string;
      icon?: string;
      emphasis?: {
        type: 'bold' | 'italic' | 'underline' | 'highlight';
        text: string;
      }[];
    }[];
    ordered?: boolean;
  };
}

export interface QuoteSection extends ContentSection {
  type: 'quote';
  content: {
    text: string;
    author: string;
    role?: string;
    company?: string;
  };
}

export interface CodeSection extends ContentSection {
  type: 'code';
  content: {
    heading?: string;
    language: string;
    code: string;
    description?: string;
  };
}

export interface CTASection extends ContentSection {
  type: 'cta';
  content: {
    heading: string;
    description: string;
    primaryButton: {
      text: string;
      href: string;
    };
    secondaryButton?: {
      text: string;
      href: string;
    };
  };
}

export interface DividerSection extends ContentSection {
  type: 'divider';
  content: {
    style?: 'line' | 'dots' | 'wave';
  };
}

export type ContentTemplate = (
  | HeroSection
  | TextSection
  | ListSection
  | QuoteSection
  | CodeSection
  | CTASection
  | DividerSection
)[];

export interface LanguageContent {
  en: ContentTemplate;
  fr: ContentTemplate;
  es: ContentTemplate;
  ar: ContentTemplate;
}

export type SupportedLanguage = keyof LanguageContent;
