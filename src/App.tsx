import ScrollToTop from '@/components/common/ScrollToTop';
import Layout from '@/components/layout/Layout';
import AppInitializer from "@/components/providers/AppInitializer";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import '@/i18n/config';
import AnalysisResultsPage from '@/pages/AnalysisResultsPage';
import HistoryPage from '@/pages/HistoryPage';
import HomePage from '@/pages/HomePage';
import UploadPage from '@/pages/UploadPage';
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { SupportWidget } from "./components/layout/SupportWidget";
import Forbidden from "./pages/Forbidden";
import InternalServerError from "./pages/InternalServerError";
import LearnMorePage from "./pages/LearnMorePage";
import NotFound from "./pages/NotFound";
import ServiceUnavailable from "./pages/ServiceUnavailable";
import { ThemeProvider } from "./themes/theme-provider";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
    >
      <SupportWidget />
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <AppInitializer>
            <Layout>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/upload" element={<UploadPage />} />
                <Route path="/learn-more" element={<LearnMorePage />} />
                <Route path="/history" element={<HistoryPage />} />
                <Route path="/analysis/:id" element={<AnalysisResultsPage />} />
                <Route path="/results/:id" element={<AnalysisResultsPage />} />

                {/* Error Pages */}
                <Route path="/403" element={<Forbidden />} />
                <Route path="/forbidden" element={<Forbidden />} />
                <Route path="/500" element={<InternalServerError />} />
                <Route path="/error" element={<InternalServerError />} />
                <Route path="/503" element={<ServiceUnavailable />} />
                <Route path="/maintenance" element={<ServiceUnavailable />} />

                {/* 404 - Must be last */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Layout>
          </AppInitializer>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
