import axios from 'axios';

export interface GeminiAnalysisRequest {
  resumeText: string;
  jobDescription?: string;
  additionalCriteria?: string[];
  language?: string;
}

export interface GeminiAnalysisResponse {
  overallScore: number;
  scores: {
    technical: number;
    experience: number;
    communication: number;
    cultureFit: number;
  };
  candidateName?: string;
  summary: string;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  skills: string[];
  experience: {
    years: number;
    positions: string[];
    companies: string[];
  };
  education: {
    degree?: string;
    institution?: string;
    year?: string;
  }[];
  keyHighlights: string[];
  redFlags: string[];
  language?: string; // Track which language this analysis is in
}

export interface MultiLanguageAnalysisResponse {
  baseAnalysis: GeminiAnalysisResponse;
  translations: {
    [languageCode: string]: GeminiAnalysisResponse;
  };
  analysisId: string;
  createdAt: string;
}

export class GeminiAPI {
  private static readonly API_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
  
  static async analyzeResume(
    request: GeminiAnalysisRequest,
    apiKey: string
  ): Promise<GeminiAnalysisResponse> {
    if (!apiKey) {
      throw new Error('Gemini API key is required');
    }

    try {
      const prompt = this.buildAnalysisPrompt(request);
      
      const response = await axios.post(
        `${this.API_BASE_URL}?key=${apiKey}`,
        {
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.2,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      if (!response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {
        throw new Error('Invalid response format from Gemini API');
      }

      const analysisText = response.data.candidates[0].content.parts[0].text;
      return this.parseAnalysisResponse(analysisText);
    } catch (error) {
      console.error('Gemini API error:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Invalid API key. Please check your Gemini API configuration.');
        } else if (error.response?.status === 429) {
          throw new Error('API rate limit exceeded. Please try again later.');
        } else if (error.response?.status >= 500) {
          throw new Error('Gemini API service is temporarily unavailable. Please try again later.');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout. Please try again.');
        }
      }
      
      throw new Error('Failed to analyze resume. Please check your internet connection and try again.');
    }
  }

  private static buildAnalysisPrompt(request: GeminiAnalysisRequest): string {
    const { resumeText, jobDescription, additionalCriteria, language = 'en' } = request;

    const languageInstructions = {
      'en': 'Provide all analysis content in English.',
      'ar': 'قدم جميع محتويات التحليل باللغة العربية.',
      'es': 'Proporciona todo el contenido del análisis en español.',
      'fr': 'Fournissez tout le contenu de l\'analyse en français.'
    };

    const languageInstruction = languageInstructions[language as keyof typeof languageInstructions] || languageInstructions['en'];

    let prompt = `
You are an expert HR professional and resume screener. Analyze the following resume text and provide a comprehensive evaluation in JSON format.

IMPORTANT: ${languageInstruction} All text fields in the JSON response (summary, strengths, weaknesses, recommendations, etc.) must be in the specified language.

RESUME TEXT:
${resumeText}

${jobDescription ? `JOB DESCRIPTION:
${jobDescription}` : ''}

${additionalCriteria?.length ? `ADDITIONAL CRITERIA:
${additionalCriteria.join('\n')}` : ''}

Please provide a detailed analysis in the following JSON format (ensure valid JSON with no additional text):

{
  "candidateName": "Extract the candidate's name from the resume",
  "overallScore": 85,
  "scores": {
    "technical": 80,
    "experience": 85,
    "communication": 90,
    "cultureFit": 80
  },
  "summary": "A brief 2-3 sentence summary of the candidate's profile and suitability",
  "strengths": ["List of 3-5 key strengths"],
  "weaknesses": ["List of 2-4 areas for improvement"],
  "recommendations": ["List of 3-5 hiring recommendations"],
  "skills": ["Extracted technical and soft skills"],
  "experience": {
    "years": 5,
    "positions": ["List of job titles"],
    "companies": ["List of company names"]
  },
  "education": [
    {
      "degree": "Bachelor's in Computer Science",
      "institution": "University Name",
      "year": "2019"
    }
  ],
  "keyHighlights": ["Notable achievements or standout points"],
  "redFlags": ["Any concerns or potential issues"]
}

SCORING GUIDELINES:
- All scores should be between 0-100
- Technical: Relevance and depth of technical skills
- Experience: Quality and relevance of work experience
- Communication: Writing quality, presentation, clarity
- Culture Fit: Professional demeanor, values alignment
- Overall: Weighted average considering all factors

Ensure the JSON is valid and properly formatted. Do not include any text outside the JSON structure.`;

    return prompt;
  }

  private static parseAnalysisResponse(analysisText: string): GeminiAnalysisResponse {
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const jsonText = jsonMatch[0];
      const parsed = JSON.parse(jsonText);

      // Validate and sanitize the response
      return {
        candidateName: parsed.candidateName || 'Unknown Candidate',
        overallScore: this.sanitizeScore(parsed.overallScore),
        scores: {
          technical: this.sanitizeScore(parsed.scores?.technical),
          experience: this.sanitizeScore(parsed.scores?.experience),
          communication: this.sanitizeScore(parsed.scores?.communication),
          cultureFit: this.sanitizeScore(parsed.scores?.cultureFit),
        },
        summary: parsed.summary || 'No summary available',
        strengths: Array.isArray(parsed.strengths) ? parsed.strengths : [],
        weaknesses: Array.isArray(parsed.weaknesses) ? parsed.weaknesses : [],
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
        skills: Array.isArray(parsed.skills) ? parsed.skills : [],
        experience: {
          years: parseInt(parsed.experience?.years) || 0,
          positions: Array.isArray(parsed.experience?.positions) ? parsed.experience.positions : [],
          companies: Array.isArray(parsed.experience?.companies) ? parsed.experience.companies : [],
        },
        education: Array.isArray(parsed.education) ? parsed.education : [],
        keyHighlights: Array.isArray(parsed.keyHighlights) ? parsed.keyHighlights : [],
        redFlags: Array.isArray(parsed.redFlags) ? parsed.redFlags : [],
      };
    } catch (error) {
      console.error('Error parsing Gemini response:', error);
      throw new Error('Failed to parse AI analysis response. Please try again.');
    }
  }

  private static sanitizeScore(score: any): number {
    const num = parseFloat(score);
    if (isNaN(num)) return 0;
    return Math.max(0, Math.min(100, num));
  }

  static async analyzeResumeMultiLanguage(
    request: GeminiAnalysisRequest,
    apiKey: string,
    languages: string[] = ['en', 'fr', 'es', 'ar']
  ): Promise<MultiLanguageAnalysisResponse> {
    if (!apiKey) {
      throw new Error('Gemini API key is required');
    }

    const analysisId = Date.now().toString();
    const createdAt = new Date().toISOString();

    // Start with the primary language (or English as default)
    const primaryLanguage = request.language || 'en';
    const baseAnalysis = await this.analyzeResume(request, apiKey);
    baseAnalysis.language = primaryLanguage;

    const translations: { [languageCode: string]: GeminiAnalysisResponse } = {};
    translations[primaryLanguage] = baseAnalysis;

    // Generate translations for other languages
    for (const lang of languages) {
      if (lang !== primaryLanguage) {
        try {
          const translatedRequest = { ...request, language: lang };
          const translatedAnalysis = await this.analyzeResume(translatedRequest, apiKey);
          translatedAnalysis.language = lang;
          translations[lang] = translatedAnalysis;

          // Add a small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          console.warn(`Failed to generate analysis for language ${lang}:`, error);
          // Continue with other languages even if one fails
        }
      }
    }

    return {
      baseAnalysis,
      translations,
      analysisId,
      createdAt
    };
  }

  static async testConnection(apiKey: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.API_BASE_URL}?key=${apiKey}`,
        {
          contents: [{
            parts: [{
              text: 'Hello, can you confirm this API key is working? Please respond with "API connection successful".'
            }]
          }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 second timeout for test
        }
      );

      return response.data?.candidates?.[0]?.content?.parts?.[0]?.text?.includes('successful') || false;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }

  static getScoreLabel(score: number): string {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'average';
    if (score >= 60) return 'below_average';
    return 'poor';
  }

  static getScoreColor(score: number): string {
    if (score >= 90) return 'success';
    if (score >= 80) return 'info';
    if (score >= 70) return 'warning';
    if (score >= 60) return 'warning';
    return 'error';
  }
}
