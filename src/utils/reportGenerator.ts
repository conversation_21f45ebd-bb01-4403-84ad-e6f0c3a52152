import { ResumeAnalysis } from './storage';

export class ReportGenerator {
  static generateHTMLReport(analysis: ResumeAnalysis): string {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Analysis Report - ${analysis.candidateName || 'Anonymous Candidate'}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1e40af;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .meta-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .meta-item {
            text-align: center;
        }
        .meta-label {
            font-size: 0.9em;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .meta-value {
            font-weight: bold;
            color: #1f2937;
        }
        .overall-score {
            text-align: center;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        .overall-score h2 {
            margin: 0 0 10px 0;
            font-size: 1.5em;
        }
        .score-circle {
            display: inline-block;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            font-weight: bold;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 30px;
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .section-header {
            background: #f9fafb;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .section-header h3 {
            margin: 0;
            color: #1f2937;
            font-size: 1.3em;
        }
        .section-content {
            padding: 20px;
        }
        .scores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .score-item {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .score-item h4 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 1em;
        }
        .score-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .score-excellent { color: #059669; }
        .score-good { color: #d97706; }
        .score-average { color: #dc2626; }
        .list-item {
            padding: 10px 0;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: flex-start;
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .list-bullet {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 12px;
            margin-top: 8px;
            flex-shrink: 0;
        }
        .strength-bullet { background: #059669; }
        .weakness-bullet { background: #dc2626; }
        .recommendation-bullet { background: #3b82f6; }
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .skill-tag {
            background: #dbeafe;
            color: #1e40af;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.9em;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .section { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Resume Analysis Report</h1>
        <p>${analysis.fileName} • Generated on ${new Date(analysis.analysisDate).toLocaleDateString()}</p>
    </div>

    <div class="meta-info">
        <div class="meta-item">
            <div class="meta-label">Candidate</div>
            <div class="meta-value">${analysis.candidateName || 'Anonymous'}</div>
        </div>
        <div class="meta-item">
            <div class="meta-label">File Size</div>
            <div class="meta-value">${(analysis.fileSize / 1024).toFixed(1)} KB</div>
        </div>
        <div class="meta-item">
            <div class="meta-label">Analysis Date</div>
            <div class="meta-value">${new Date(analysis.analysisDate).toLocaleDateString()}</div>
        </div>
        <div class="meta-item">
            <div class="meta-label">Experience</div>
            <div class="meta-value">${analysis.experience.years} years</div>
        </div>
    </div>

    <div class="overall-score">
        <h2>Overall Match Score</h2>
        <div class="score-circle">${analysis.overallScore}%</div>
        <p>${analysis.summary}</p>
    </div>

    <div class="section">
        <div class="section-header">
            <h3>Score Breakdown</h3>
        </div>
        <div class="section-content">
            <div class="scores-grid">
                <div class="score-item">
                    <h4>Technical Skills</h4>
                    <div class="score-value ${this.getScoreClass(analysis.scores.technical)}">${analysis.scores.technical}%</div>
                    <div>${this.getScoreLabel(analysis.scores.technical)}</div>
                </div>
                <div class="score-item">
                    <h4>Experience Level</h4>
                    <div class="score-value ${this.getScoreClass(analysis.scores.experience)}">${analysis.scores.experience}%</div>
                    <div>${this.getScoreLabel(analysis.scores.experience)}</div>
                </div>
                <div class="score-item">
                    <h4>Communication</h4>
                    <div class="score-value ${this.getScoreClass(analysis.scores.communication)}">${analysis.scores.communication}%</div>
                    <div>${this.getScoreLabel(analysis.scores.communication)}</div>
                </div>
                <div class="score-item">
                    <h4>Culture Fit</h4>
                    <div class="score-value ${this.getScoreClass(analysis.scores.cultureFit)}">${analysis.scores.cultureFit}%</div>
                    <div>${this.getScoreLabel(analysis.scores.cultureFit)}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-header">
            <h3>Key Strengths</h3>
        </div>
        <div class="section-content">
            ${analysis.strengths.map(strength => `
                <div class="list-item">
                    <div class="list-bullet strength-bullet"></div>
                    <div>${strength}</div>
                </div>
            `).join('')}
        </div>
    </div>

    <div class="section">
        <div class="section-header">
            <h3>Areas for Improvement</h3>
        </div>
        <div class="section-content">
            ${analysis.weaknesses.map(weakness => `
                <div class="list-item">
                    <div class="list-bullet weakness-bullet"></div>
                    <div>${weakness}</div>
                </div>
            `).join('')}
        </div>
    </div>

    <div class="section">
        <div class="section-header">
            <h3>Skills Identified</h3>
        </div>
        <div class="section-content">
            <div class="skills-container">
                ${analysis.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
            </div>
        </div>
    </div>

    ${analysis.recommendations.length > 0 ? `
    <div class="section">
        <div class="section-header">
            <h3>Hiring Recommendations</h3>
        </div>
        <div class="section-content">
            ${analysis.recommendations.map((rec, index) => `
                <div class="list-item">
                    <div class="list-bullet recommendation-bullet"></div>
                    <div><strong>${index + 1}.</strong> ${rec}</div>
                </div>
            `).join('')}
        </div>
    </div>
    ` : ''}

    <div class="footer">
        <p>Generated by 4CV - AI-Powered Resume Analysis Platform</p>
        <p>Report ID: ${analysis.id}</p>
    </div>
</body>
</html>`;
    return html;
  }

  private static getScoreClass(score: number): string {
    if (score >= 80) return 'score-excellent';
    if (score >= 60) return 'score-good';
    return 'score-average';
  }

  private static getScoreLabel(score: number): string {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Average';
    return 'Below Average';
  }

  static downloadHTMLReport(analysis: ResumeAnalysis): void {
    const html = this.generateHTMLReport(analysis);
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `resume-analysis-${analysis.candidateName || 'anonymous'}-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  static downloadJSONReport(analysis: ResumeAnalysis): void {
    const jsonData = JSON.stringify(analysis, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `resume-analysis-${analysis.candidateName || 'anonymous'}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  static async printReport(analysis: ResumeAnalysis): Promise<void> {
    const html = this.generateHTMLReport(analysis);
    const printWindow = window.open('', '_blank');
    
    if (printWindow) {
      printWindow.document.write(html);
      printWindow.document.close();
      
      // Wait for content to load before printing
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }
  }
}
