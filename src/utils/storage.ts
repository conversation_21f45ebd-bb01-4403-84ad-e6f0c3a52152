import Cookies from 'js-cookie';

// localStorage utilities for app data
export class LocalStorage {
  private static prefix = 'resume-screener-';

  static set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(this.prefix + key, serializedValue);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  static get<T>(key: string, defaultValue?: T): T | undefined {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (item === null) return defaultValue;
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  }

  static remove(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }

  static clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  static getAllAnalyses(): ResumeAnalysis[] {
    return this.get<ResumeAnalysis[]>('analyses', []) || [];
  }

  static saveAnalysis(analysis: ResumeAnalysis): void {
    const analyses = this.getAllAnalyses();
    const existingIndex = analyses.findIndex(a => a.id === analysis.id);
    
    if (existingIndex !== -1) {
      analyses[existingIndex] = analysis;
    } else {
      analyses.push(analysis);
    }
    
    this.set('analyses', analyses);
  }

  static deleteAnalysis(id: string): void {
    const analyses = this.getAllAnalyses();
    const filtered = analyses.filter(a => a.id !== id);
    this.set('analyses', filtered);
  }

  static clearAllAnalyses(): void {
    this.set('analyses', []);
  }

  static exportData(): string {
    const data = {
      analyses: this.getAllAnalyses(),
      settings: this.get('settings', {}),
      exportDate: new Date().toISOString(),
      version: '1.0'
    };
    return JSON.stringify(data, null, 2);
  }

  static importData(jsonString: string): boolean {
    try {
      const data = JSON.parse(jsonString);
      if (data.analyses && Array.isArray(data.analyses)) {
        this.set('analyses', data.analyses);
      }
      if (data.settings) {
        this.set('settings', data.settings);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  static getAnalysisInLanguage(analysisId: string, language: string): ResumeAnalysis | null {
    const analyses = this.getAllAnalyses();
    const analysis = analyses.find(a => a.id === analysisId);

    if (!analysis) return null;

    // If no multi-language data exists, return the original analysis
    if (!analysis.multiLanguageData || !analysis.multiLanguageData[language]) {
      return analysis;
    }

    // Create a copy with the requested language data
    const languageData = analysis.multiLanguageData[language];
    return {
      ...analysis,
      summary: languageData.summary,
      strengths: languageData.strengths,
      weaknesses: languageData.weaknesses,
      recommendations: languageData.recommendations,
      skills: languageData.skills,
      redFlags: languageData.redFlags,
      candidateName: languageData.candidateName || analysis.candidateName
    };
  }

  static saveMultiLanguageAnalysis(
    baseAnalysis: ResumeAnalysis,
    multiLanguageData: { [languageCode: string]: any }
  ): void {
    const enhancedAnalysis: ResumeAnalysis = {
      ...baseAnalysis,
      multiLanguageData,
      primaryLanguage: baseAnalysis.primaryLanguage || 'en'
    };

    this.saveAnalysis(enhancedAnalysis);
  }
}

// Cookie utilities for user preferences
export class CookieStorage {
  private static prefix = 'resume-screener-';
  private static options = {
    expires: 365, // 1 year
    sameSite: 'strict' as const,
    secure: window.location.protocol === 'https:'
  };

  static setTheme(theme: 'light' | 'dark'): void {
    Cookies.set(this.prefix + 'theme', theme, this.options);
  }

  static getTheme(): 'light' | 'dark' {
    const savedTheme = Cookies.get(this.prefix + 'theme') as 'light' | 'dark' | 'auto';
    // Convert auto to light as default if auto was previously saved
    return savedTheme === 'auto' ? 'light' : (savedTheme || 'light');
  }

  static setLanguage(language: string): void {
    Cookies.set(this.prefix + 'language', language, this.options);
  }

  static getLanguage(): string {
    return Cookies.get(this.prefix + 'language') || 'en';
  }

  static setApiKey(apiKey: string): void {
    // Store API key securely (in production, consider more secure storage)
    Cookies.set(this.prefix + 'api-key', apiKey, {
      ...this.options,
      secure: true,
      httpOnly: false // Needed for client-side access
    });
  }

  static getApiKey(): string | undefined {
    const storedKey = Cookies.get(this.prefix + 'api-key');
    // Return stored key if exists, otherwise return default key
    return storedKey || 'AIzaSyCU6wEazb7dvTZnVV9BtaFk39sg52d4-IQ';
  }

  static removeApiKey(): void {
    Cookies.remove(this.prefix + 'api-key');
  }

  static setCookieConsent(consent: boolean): void {
    Cookies.set(this.prefix + 'cookie-consent', consent.toString(), {
      ...this.options,
      expires: 365 // Cookie consent lasts for 1 year
    });
  }

  static getCookieConsent(): boolean | null {
    const consent = Cookies.get(this.prefix + 'cookie-consent');
    if (consent === undefined) return null;
    return consent === 'true';
  }

  static clearAllData(): void {
    Object.keys(Cookies.get()).forEach(key => {
      if (key.startsWith(this.prefix) && !key.includes('cookie-consent')) {
        Cookies.remove(key);
      }
    });
  }

  static clearAll(): void {
    Object.keys(Cookies.get()).forEach(key => {
      if (key.startsWith(this.prefix)) {
        Cookies.remove(key);
      }
    });
  }
}

// Type definitions
export interface ResumeAnalysis {
  id: string;
  fileName: string;
  fileSize: number;
  uploadDate: string;
  analysisDate: string;
  candidateName?: string;
  overallScore: number;
  scores: {
    technical: number;
    experience: number;
    communication: number;
    cultureFit: number;
  };
  summary: string;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  skills: string[];
  experience: {
    years: number;
    positions: string[];
    companies: string[];
  };
  education: {
    degree?: string;
    institution?: string;
    year?: string;
  }[];
  redFlags: string[];
  rawText: string;
  geminiResponse?: any;
  // Multi-language support
  multiLanguageData?: {
    [languageCode: string]: {
      summary: string;
      strengths: string[];
      weaknesses: string[];
      recommendations: string[];
      skills: string[];
      redFlags: string[];
      candidateName?: string;
    };
  };
  primaryLanguage?: string;
}

export interface AppSettings {
  theme: 'light' | 'dark';
  language: string;
  apiKey?: string;
  autoSave: boolean;
  notifications: boolean;
}

export const defaultSettings: AppSettings = {
  theme: 'light',
  language: 'en',
  autoSave: true,
  notifications: true
};
