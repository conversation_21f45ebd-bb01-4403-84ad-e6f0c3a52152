// Use the correct import path for web-vitals v5+
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';
/**
 * Performance monitoring utilities using Web Vitals
 */

// Type for performance metrics
export interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

// Store metrics for reporting
const metrics: PerformanceMetric[] = [];

/**
 * Report a performance metric
 */
const reportMetric = (metric: PerformanceMetric) => {
  metrics.push(metric);

  // Log to console in development
  if (import.meta.env.DEV) {
    console.log(`Performance Metric - ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      timestamp: new Date(metric.timestamp).toISOString()
    });
  }

  // In production, send to analytics if needed
  // Example: sendToAnalytics(metric);
};

/**
 * Initialize performance monitoring
 */
export const initPerformanceMonitoring = () => {
  // Cumulative Layout Shift
  onCLS((metric) => {
    reportMetric({
      name: 'CLS',
      value: metric.value,
      rating: metric.rating as PerformanceMetric['rating'],
      timestamp: Date.now()
    });
  });

  // Interaction to Next Paint (replaces FID in v5)
  onINP((metric) => {
    reportMetric({
      name: 'INP',
      value: metric.value,
      rating: metric.rating as PerformanceMetric['rating'],
      timestamp: Date.now()
    });
  });

  // First Contentful Paint
  onFCP((metric) => {
    reportMetric({
      name: 'FCP',
      value: metric.value,
      rating: metric.rating as PerformanceMetric['rating'],
      timestamp: Date.now()
    });
  });

  // Largest Contentful Paint
  onLCP((metric) => {
    reportMetric({
      name: 'LCP',
      value: metric.value,
      rating: metric.rating as PerformanceMetric['rating'],
      timestamp: Date.now()
    });
  });

  // Time to First Byte
  onTTFB((metric) => {
    reportMetric({
      name: 'TTFB',
      value: metric.value,
      rating: metric.rating as PerformanceMetric['rating'],
      timestamp: Date.now()
    });
  });
};

/**
 * Get all collected metrics
 */
export const getPerformanceMetrics = (): PerformanceMetric[] => {
  return [...metrics];
};

/**
 * Get performance summary
 */
export const getPerformanceSummary = () => {
  const summary = {
    good: 0,
    needsImprovement: 0,
    poor: 0,
    total: metrics.length
  };

  metrics.forEach(metric => {
    switch (metric.rating) {
      case 'good':
        summary.good++;
        break;
      case 'needs-improvement':
        summary.needsImprovement++;
        break;
      case 'poor':
        summary.poor++;
        break;
    }
  });

  return summary;
};

/**
 * Custom performance timing for specific operations
 */
export const measurePerformance = async <T>(
  name: string,
  operation: () => T | Promise<T>
): Promise<{ result: T; duration: number }> => {
  const start = performance.now();

  try {
    const result = operation();

    if (result instanceof Promise) {
      const awaited = await result;
      const duration = performance.now() - start;

      if (import.meta.env.DEV) {
        console.log(`Performance - ${name}: ${duration.toFixed(2)}ms`);
      }
      return { result: awaited, duration };
    } else {
      const duration = performance.now() - start;

      if (import.meta.env.DEV) {
        console.log(`Performance - ${name}: ${duration.toFixed(2)}ms`);
      }
      return { result, duration };
    }
  } catch (error) {
    const duration = performance.now() - start;

    if (import.meta.env.DEV) {
      console.error(`Performance - ${name} failed after ${duration.toFixed(2)}ms:`, error);
    }

    throw error;
  }
};
