import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker
if (typeof window !== 'undefined') {
  // Use local worker file to avoid CORS issues
  pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
}

export interface PDFInfo {
  text: string;
  numPages: number;
  metadata?: {
    title?: string;
    author?: string;
    subject?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modDate?: Date;
  };
}

export class PDFParser {
  static async extractTextFromFile(file: File): Promise<PDFInfo> {
    try {
      // Convert file to ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      
      // Load PDF document
      const pdf = await pdfjsLib.getDocument({
        data: arrayBuffer,
        verbosity: 0 // Suppress warnings
      }).promise;

      // Extract metadata
      const metadata = await this.extractMetadata(pdf);
      
      // Extract text from all pages
      const text = await this.extractAllText(pdf);
      
      return {
        text: text.trim(),
        numPages: pdf.numPages,
        metadata
      };
    } catch (error) {
      console.error('PDF parsing error:', error);
      throw new Error('Failed to extract text from PDF. Please ensure the file is a valid PDF document.');
    }
  }

  private static async extractMetadata(pdf: any): Promise<PDFInfo['metadata']> {
    try {
      const metadata = await pdf.getMetadata();
      return {
        title: metadata.info?.Title,
        author: metadata.info?.Author,
        subject: metadata.info?.Subject,
        creator: metadata.info?.Creator,
        producer: metadata.info?.Producer,
        creationDate: metadata.info?.CreationDate ? new Date(metadata.info.CreationDate) : undefined,
        modDate: metadata.info?.ModDate ? new Date(metadata.info.ModDate) : undefined,
      };
    } catch (error) {
      console.warn('Could not extract PDF metadata:', error);
      return {};
    }
  }

  private static async extractAllText(pdf: any): Promise<string> {
    const textContent: string[] = [];
    
    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const pageText = await this.extractPageText(page);
        if (pageText.trim()) {
          textContent.push(pageText);
        }
      } catch (error) {
        console.warn(`Error extracting text from page ${pageNum}:`, error);
        // Continue with other pages
      }
    }
    
    return textContent.join('\n\n');
  }

  private static async extractPageText(page: any): Promise<string> {
    const textContent = await page.getTextContent();
    
    // Sort text items by position (top to bottom, left to right)
    const sortedItems = textContent.items.sort((a: any, b: any) => {
      const yDiff = b.transform[5] - a.transform[5]; // Y position (top to bottom)
      if (Math.abs(yDiff) > 5) { // Different lines
        return yDiff > 0 ? 1 : -1;
      }
      return a.transform[4] - b.transform[4]; // X position (left to right)
    });

    // Extract text with better spacing
    let previousY = null;
    const lines: string[] = [];
    let currentLine = '';

    for (const item of sortedItems) {
      const y = item.transform[5];
      const text = item.str.trim();
      
      if (!text) continue;

      // Check if this is a new line
      if (previousY !== null && Math.abs(y - previousY) > 5) {
        if (currentLine.trim()) {
          lines.push(currentLine.trim());
        }
        currentLine = text;
      } else {
        // Same line, add space if needed
        if (currentLine && !currentLine.endsWith(' ') && !text.startsWith(' ')) {
          currentLine += ' ';
        }
        currentLine += text;
      }
      
      previousY = y;
    }

    // Add the last line
    if (currentLine.trim()) {
      lines.push(currentLine.trim());
    }

    return lines.join('\n');
  }

  static async validatePDF(file: File): Promise<{ isValid: boolean; error?: string }> {
    try {
      // Check file type
      if (file.type !== 'application/pdf') {
        return { isValid: false, error: 'File must be a PDF document' };
      }

      // Check file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        return { isValid: false, error: 'File size must be less than 5MB' };
      }

      // Try to load the PDF to verify it's valid
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({
        data: arrayBuffer,
        verbosity: 0
      }).promise;

      // Check if PDF has pages
      if (pdf.numPages === 0) {
        return { isValid: false, error: 'PDF document appears to be empty' };
      }

      return { isValid: true };
    } catch (error) {
      console.error('PDF validation error:', error);
      return { 
        isValid: false, 
        error: 'Invalid PDF document. Please ensure the file is not corrupted.' 
      };
    }
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static async getFileInfo(file: File): Promise<{
    name: string;
    size: string;
    type: string;
    lastModified: Date;
  }> {
    return {
      name: file.name,
      size: this.formatFileSize(file.size),
      type: file.type,
      lastModified: new Date(file.lastModified)
    };
  }
}
