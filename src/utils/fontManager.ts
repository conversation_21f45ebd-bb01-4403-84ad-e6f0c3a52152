export interface FontConfig {
  name: string;
  family: string;
  weights: number[];
  fallbacks: string[];
  googleFontUrl: string;
  cssVariable: string;
  rtl?: boolean;
}

export interface LanguageFontMapping {
  [languageCode: string]: FontConfig;
}

// Professional font selection for each language
export const LANGUAGE_FONTS: LanguageFontMapping = {
  en: {
    name: 'Inter',
    family: 'Inter',
    weights: [400, 500, 600, 700],
    fallbacks: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    cssVariable: '--font-family-en',
    rtl: false
  },
  fr: {
    name: 'Poppins',
    family: 'Poppins',
    weights: [400, 500, 600, 700],
    fallbacks: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap',
    cssVariable: '--font-family-fr',
    rtl: false
  },
  es: {
    name: 'Nunito Sans',
    family: 'Nunito Sans',
    weights: [400, 500, 600, 700],
    fallbacks: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;500;600;700&display=swap',
    cssVariable: '--font-family-es',
    rtl: false
  },
  ar: {
    name: 'Cairo',
    family: 'Cairo',
    weights: [400, 500, 600, 700],
    fallbacks: ['Tahoma', 'Arial', 'sans-serif'],
    googleFontUrl: 'https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap',
    cssVariable: '--font-family-ar',
    rtl: true
  }
};

export class FontManager {
  private static loadedFonts = new Set<string>();
  private static currentLanguage: string | null = null;

  /**
   * Load a Google Font dynamically
   */
  static async loadFont(fontConfig: FontConfig): Promise<void> {
    const fontKey = `${fontConfig.name}-${fontConfig.weights.join('-')}`;
    
    if (this.loadedFonts.has(fontKey)) {
      return; // Font already loaded
    }

    return new Promise((resolve, reject) => {
      // Check if link already exists
      const existingLink = document.querySelector(`link[href="${fontConfig.googleFontUrl}"]`);
      if (existingLink) {
        this.loadedFonts.add(fontKey);
        resolve();
        return;
      }

      // Create and append link element
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = fontConfig.googleFontUrl;
      link.crossOrigin = 'anonymous';

      link.onload = () => {
        this.loadedFonts.add(fontKey);
        console.log(`✅ Font loaded: ${fontConfig.name}`);
        resolve();
      };

      link.onerror = () => {
        console.error(`❌ Failed to load font: ${fontConfig.name}`);
        reject(new Error(`Failed to load font: ${fontConfig.name}`));
      };

      // Add to document head
      document.head.appendChild(link);

      // Preload font for better performance
      this.preloadFont(fontConfig);
    });
  }

  /**
   * Preload font for better performance
   */
  private static preloadFont(fontConfig: FontConfig): void {
    // Create preload link for CSS
    const preloadLink = document.createElement('link');
    preloadLink.rel = 'preload';
    preloadLink.as = 'style';
    preloadLink.href = fontConfig.googleFontUrl;
    preloadLink.crossOrigin = 'anonymous';
    document.head.appendChild(preloadLink);

    // Create font-display: swap for better performance
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: '${fontConfig.family}';
        font-display: swap;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Apply font to the document
   */
  static applyFont(languageCode: string): void {
    const fontConfig = LANGUAGE_FONTS[languageCode];
    if (!fontConfig) {
      console.warn(`No font configuration found for language: ${languageCode}`);
      return;
    }

    // Create font family string with fallbacks
    const fontFamily = `"${fontConfig.family}", ${fontConfig.fallbacks.join(', ')}`;

    // Set CSS custom property
    document.documentElement.style.setProperty(fontConfig.cssVariable, fontFamily);
    document.documentElement.style.setProperty('--font-family-current', fontFamily);

    // Set direction for RTL languages
    if (fontConfig.rtl) {
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.style.setProperty('--text-direction', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.style.setProperty('--text-direction', 'ltr');
    }

    this.currentLanguage = languageCode;
    console.log(`🎨 Applied font: ${fontConfig.name} for language: ${languageCode}`);
  }

  /**
   * Switch language font with smooth transition
   */
  static async switchLanguageFont(languageCode: string): Promise<void> {
    const fontConfig = LANGUAGE_FONTS[languageCode];
    if (!fontConfig) {
      console.warn(`No font configuration found for language: ${languageCode}`);
      return;
    }

    try {
      // Load the font first
      await this.loadFont(fontConfig);
      
      // Apply with smooth transition
      document.body.style.transition = 'font-family 0.3s ease-in-out';
      this.applyFont(languageCode);
      
      // Remove transition after animation
      setTimeout(() => {
        document.body.style.transition = '';
      }, 300);

    } catch (error) {
      console.error('Failed to switch font:', error);
      // Apply font anyway with fallbacks
      this.applyFont(languageCode);
    }
  }

  /**
   * Preload all language fonts for better performance
   */
  static async preloadAllFonts(): Promise<void> {
    const loadPromises = Object.values(LANGUAGE_FONTS).map(fontConfig => 
      this.loadFont(fontConfig).catch(error => {
        console.warn(`Failed to preload font ${fontConfig.name}:`, error);
      })
    );

    await Promise.allSettled(loadPromises);
    console.log('🚀 All language fonts preloaded');
  }

  /**
   * Get font configuration for a language
   */
  static getFontConfig(languageCode: string): FontConfig | null {
    return LANGUAGE_FONTS[languageCode] || null;
  }

  /**
   * Get current language font
   */
  static getCurrentLanguage(): string | null {
    return this.currentLanguage;
  }

  /**
   * Check if font is loaded
   */
  static isFontLoaded(fontConfig: FontConfig): boolean {
    const fontKey = `${fontConfig.name}-${fontConfig.weights.join('-')}`;
    return this.loadedFonts.has(fontKey);
  }

  /**
   * Initialize font system with default language
   */
  static async initialize(defaultLanguage: string = 'en'): Promise<void> {
    try {
      // Preload all fonts in background
      this.preloadAllFonts();
      
      // Apply default language font immediately
      await this.switchLanguageFont(defaultLanguage);
      
      console.log('🎯 Font system initialized');
    } catch (error) {
      console.error('Failed to initialize font system:', error);
    }
  }
}

export default FontManager;
