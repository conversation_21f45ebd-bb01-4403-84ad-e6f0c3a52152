// src/utils/helper.ts
export const scrollToTop = (behavior: ScrollBehavior = "smooth") => {
  if (typeof window !== "undefined") {
    window.scrollTo({ top: 0, left: 0, behavior });
  }
};

export const navigateAndScrollTop = (
  navigate: (to: string) => void,
  currentPathname: string,
  href: string,
  behavior: ScrollBehavior = "smooth"
) => {
  if (currentPathname !== href) {
    navigate(href);
    // Ensure scrolling happens after the route updates
    requestAnimationFrame(() => scrollToTop(behavior));
  } else {
    // Same route: just scroll to top
    scrollToTop(behavior);
  }
};
