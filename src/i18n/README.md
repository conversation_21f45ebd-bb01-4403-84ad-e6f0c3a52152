# Internationalization (i18n) Structure

This document describes the component-based internationalization structure used in the 4CV application.

## Overview

The i18n system has been refactored from large monolithic JSON files to smaller, component-based files for better maintainability and collaboration among translators.

## Directory Structure

```
src/i18n/
├── config.ts                 # i18n configuration and setup
├── README.md                 # This documentation
└── locales/
    ├── en/                   # English translations
    │   ├── header.json       # Navigation, logo, app info
    │   ├── footer.json       # Footer links, copyright, creator attribution
    │   ├── upload.json       # File upload, dropzone, PDF processing
    │   ├── analysis.json     # Analysis results, loading states, AI content
    │   ├── common.json       # Shared UI elements (buttons, forms, notifications)
    │   ├── pages.json        # Page-specific content (home, not found, hero)
    │   ├── settings.json     # Theme, language, configuration options
    │   └── cookies.json      # Cookie consent and privacy content
    ├── ar/                   # Arabic translations (same structure)
    ├── es/                   # Spanish translations (same structure)
    └── fr/                   # French translations (same structure)
```

## Component Files Description

### `header.json`
Contains translations for:
- App title, subtitle, and description
- Navigation menu items
- Header-related content

### `footer.json`
Contains translations for:
- Footer sections and links
- Company information
- Creator attribution
- Contact information
- Legal links

### `upload.json`
Contains translations for:
- File upload interface
- Dropzone messages
- File information display
- Upload status messages
- Error messages
- Upload page content

### `analysis.json`
Contains translations for:
- Analysis results display
- Loading states and progress
- Score categories and labels
- Analysis actions
- AI-related content

### `common.json`
Contains translations for:
- Common UI elements (buttons, forms)
- Notifications and toast messages
- Shared actions and states
- Generic terms used across components

### `pages.json`
Contains translations for:
- Hero section content
- Home page features
- Page-specific content
- 404 error page
- Call-to-action sections

### `settings.json`
Contains translations for:
- Theme settings
- Language selection
- API configuration
- Settings-related content

### `cookies.json`
Contains translations for:
- Cookie consent interface
- Cookie type descriptions
- Privacy-related content
- Cookie management actions

## Adding New Translations

### For Existing Components

1. Navigate to the appropriate component file (e.g., `header.json` for navigation changes)
2. Add your translation key following the existing structure
3. Update the same key in all language files (`en`, `ar`, `es`, `fr`)

### For New Components

1. Create a new JSON file in each language directory
2. Add the import statement in `src/i18n/config.ts`
3. Include the new file in the `mergeTranslations` function for each language

Example:
```typescript
// Add import
import enNewComponent from './locales/en/new-component.json';

// Add to mergeTranslations
en: { 
  translation: mergeTranslations(
    enHeader,
    enFooter,
    // ... other components
    enNewComponent  // Add here
  )
}
```

## Translation Key Structure

Maintain consistent key structures across all files:

```json
{
  "componentName": {
    "section": {
      "subsection": "Translation text",
      "actions": {
        "save": "Save",
        "cancel": "Cancel"
      }
    }
  }
}
```

## Supported Languages

- **English (en)**: Primary language, used as fallback
- **Arabic (ar)**: Right-to-left language support
- **Spanish (es)**: Spanish translations
- **French (fr)**: French translations

## Best Practices

1. **Consistency**: Keep the same key structure across all language files
2. **Context**: Use descriptive key names that provide context
3. **Nesting**: Group related translations under logical sections
4. **Fallback**: English is used as the fallback language
5. **Testing**: Test translations in all supported languages
6. **Collaboration**: Each component file can be worked on independently by different translators

## Configuration

The i18n system is configured in `src/i18n/config.ts` which:
- Imports all component translation files
- Merges them into complete language resources
- Configures i18next with proper fallback and interpolation settings
- Handles language persistence via cookies

## Usage in Components

Use the `useLanguage` hook to access translations:

```typescript
import { useLanguage } from '@/hooks/useLanguage';

const MyComponent = () => {
  const { t } = useLanguage();
  
  return (
    <div>
      <h1>{t('header.app.title')}</h1>
      <p>{t('upload.description')}</p>
    </div>
  );
};
```

## Maintenance

When adding new features:
1. Identify which component file the translations belong to
2. Add translations to all language files
3. Test the feature in all supported languages
4. Update this documentation if new component files are added

This structure makes it easier for translators to work on specific sections without conflicts and improves the overall maintainability of the internationalization system.
