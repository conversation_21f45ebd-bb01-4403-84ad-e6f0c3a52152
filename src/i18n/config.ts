import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Cookies from 'js-cookie';

// Import component-based language files
// English
import enHeader from './locales/en/header.json';
import enFooter from './locales/en/footer.json';
import enUpload from './locales/en/upload.json';
import enAnalysis from './locales/en/analysis.json';
import enCommon from './locales/en/common.json';
import enPages from './locales/en/pages.json';
import enSettings from './locales/en/settings.json';
import enCookies from './locales/en/cookies.json';

// Arabic
import arHeader from './locales/ar/header.json';
import arFooter from './locales/ar/footer.json';
import arUpload from './locales/ar/upload.json';
import arAnalysis from './locales/ar/analysis.json';
import arCommon from './locales/ar/common.json';
import arPages from './locales/ar/pages.json';
import arSettings from './locales/ar/settings.json';
import arCookies from './locales/ar/cookies.json';

// Spanish
import esHeader from './locales/es/header.json';
import esFooter from './locales/es/footer.json';
import esUpload from './locales/es/upload.json';
import esAnalysis from './locales/es/analysis.json';
import esCommon from './locales/es/common.json';
import esPages from './locales/es/pages.json';
import esSettings from './locales/es/settings.json';
import esCookies from './locales/es/cookies.json';

// French
import frHeader from './locales/fr/header.json';
import frFooter from './locales/fr/footer.json';
import frUpload from './locales/fr/upload.json';
import frAnalysis from './locales/fr/analysis.json';
import frCommon from './locales/fr/common.json';
import frPages from './locales/fr/pages.json';
import frSettings from './locales/fr/settings.json';
import frCookies from './locales/fr/cookies.json';

// Function to merge component translations
const mergeTranslations = (...translations: any[]) => {
  return translations.reduce((merged, translation) => {
    return { ...merged, ...translation };
  }, {});
};

// Get saved language from cookies or default to English
const savedLanguage = Cookies.get('resume-screener-language') || 'en';

// Merge component-based translations for each language
const resources = {
  en: {
    translation: mergeTranslations(
      enHeader,
      enFooter,
      enUpload,
      enAnalysis,
      enCommon,
      enPages,
      enSettings,
      enCookies
    )
  },
  ar: {
    translation: mergeTranslations(
      arHeader,
      arFooter,
      arUpload,
      arAnalysis,
      arCommon,
      arPages,
      arSettings,
      arCookies
    )
  },
  es: {
    translation: mergeTranslations(
      esHeader,
      esFooter,
      esUpload,
      esAnalysis,
      esCommon,
      esPages,
      esSettings,
      esCookies
    )
  },
  fr: {
    translation: mergeTranslations(
      frHeader,
      frFooter,
      frUpload,
      frAnalysis,
      frCommon,
      frPages,
      frSettings,
      frCookies
    )
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: savedLanguage,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already escapes by default
    },
    react: {
      useSuspense: false, // Avoid suspense for immediate rendering
    },
  });

export default i18n;
