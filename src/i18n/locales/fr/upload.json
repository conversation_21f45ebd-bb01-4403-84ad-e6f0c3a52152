{"upload": {"meta": {"title": "Télécharger CV pour Analyse", "description": "Téléchargez votre CV PDF pour une analyse instantanée par IA et des insights complets de candidats"}, "title": "Télécharger un CV pour Analyse", "description": "Glissez-déposez un CV PDF ou cliquez pour sélectionner un fichier", "dropzone": {"active": "Déposez votre CV ici...", "inactive": "Glissez<PERSON><PERSON><PERSON><PERSON><PERSON> le CV ici, ou cliquez pour sélectionner", "accepted": "Fichiers PDF uniquement (max 5MB)", "error": "Type de fichier invalide. Veuillez télécharger un fichier PDF."}, "file_info": {"name": "Nom du Fichier", "size": "<PERSON><PERSON>", "type": "Type", "pages": "Pages"}, "actions": {"remove": "<PERSON><PERSON><PERSON><PERSON> le Fichier", "analyze": "Analyser le CV", "upload_another": "Télécharger un Autre"}, "status": {"uploading": "Téléchargement...", "parsing": "Extraction du texte...", "analyzing": "Analyse IA en cours...", "complete": "<PERSON><PERSON><PERSON>", "success": "Téléchargement réussi", "error": "<PERSON><PERSON><PERSON>"}, "errors": {"file_too_large": "La taille du fichier dépasse la limite de 5MB", "invalid_format": "Veuillez télécharger un fichier PDF valide", "parse_failed": "Échec de l'extraction du texte du PDF", "api_error": "L'analyse IA a échoué. Veuillez réessayer.", "network_error": "<PERSON><PERSON><PERSON> réseau. Vérifiez votre connexion.", "no_api_key": "Clé API non configurée. Veuillez vérifier vos paramètres.", "cookie_consent_required": "Veuillez accepter les cookies pour utiliser la fonction d'analyse. Les cookies sont nécessaires pour sauvegarder vos préférences et résultats d'analyse."}}, "upload_page": {"title": "Télécharger un CV pour Analyse", "description": "Obtenez des insights instantanés alimentés par l'IA sur l'adéquation des candidats et les compétences"}}