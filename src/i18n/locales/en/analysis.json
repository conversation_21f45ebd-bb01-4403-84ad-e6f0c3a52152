{"analysis": {"labels": {"excellent": "Excellent", "good": "Good", "average": "Average", "below_average": "Below Average"}, "loading": {"title": "Analyzing Resume", "complete": "Complete", "tip_title": "Did you know?", "tip_description": "Our AI analyzes over 50 different criteria to provide comprehensive insights about candidates.", "steps": {"parsing": "Parsing Document", "parsing_desc": "Extracting and processing text content", "analyzing": "AI Analysis", "analyzing_desc": "Evaluating skills, experience, and qualifications", "generating": "Generating Report", "generating_desc": "Creating detailed insights and recommendations"}}, "results": {"title": "Analysis Results", "anonymous_candidate": "Anonymous Candidate", "overall_score": "Overall Match Score", "score_breakdown": "Score Breakdown", "strengths": "Key Strengths", "areas_for_improvement": "Areas for Improvement", "skills": "Skills & Technologies", "experience": "Experience Summary", "years_experience": "Years of Experience", "companies": "Previous Companies", "recommendations": "Hiring Recommendations", "red_flags": "Potential Concerns", "loading": "Loading Analysis", "loading_message": "Please wait while we retrieve your analysis data...", "no_data_found": "Analysis Not Found", "no_data_message": "The requested analysis could not be found. It may have been deleted or the link is invalid.", "not_found": "Analysis not found in storage", "load_error": "Failed to load analysis data"}, "categories": {"technical": "Technical Skills", "experience": "Experience", "communication": "Communication", "culture_fit": "Culture Fit"}, "actions": {"download": "Download Report", "save": "Save Analysis", "view_details": "View Details"}}}