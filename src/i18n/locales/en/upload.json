{"upload": {"meta": {"title": "Upload Resume for Analysis", "description": "Upload your PDF resume for instant AI-powered analysis and comprehensive candidate insights"}, "title": "Upload Resume for Analysis", "description": "Drag and drop a PDF resume or click to select a file", "dropzone": {"active": "Drop your resume here...", "inactive": "Drag & drop resume here, or click to select", "accepted": "PDF files only (max 5MB)", "error": "Invalid file type. Please upload a PDF file."}, "file_info": {"name": "File Name", "size": "Size", "type": "Type", "pages": "Pages"}, "actions": {"remove": "Remove File", "analyze": "Analyze Resume", "upload_another": "Upload Another"}, "status": {"uploading": "Uploading...", "parsing": "Extracting text...", "analyzing": "AI Analysis in progress...", "complete": "Analysis Complete", "success": "Upload successful", "error": "Analysis Failed"}, "errors": {"file_too_large": "File size exceeds 5MB limit", "invalid_format": "Please upload a valid PDF file", "parse_failed": "Failed to extract text from PDF", "api_error": "AI analysis failed. Please try again.", "network_error": "Network error. Check your connection.", "no_api_key": "API key not configured. Please check your settings.", "cookie_consent_required": "Please accept cookies to use the analysis feature. Cookies are required to save your preferences and analysis results."}}, "upload_page": {"title": "Upload Resume for Analysis", "description": "Get instant AI-powered insights on candidate fit and skills"}}