{"hero": {"headline": "Transform Your Hiring Process with AI", "subheadline": "Get instant, comprehensive candidate insights with our advanced AI-powered resume analysis platform.", "cta_primary": "Get Started", "cta_secondary": "Learn More", "features": {"ai_powered": "AI-Powered Analysis", "ai_description": "Advanced machine learning algorithms analyze resumes for skills, experience, and fit.", "instant_results": "Instant Results", "instant_description": "Get comprehensive candidate insights within seconds of upload.", "secure_private": "Secure & Private", "secure_description": "All data processed locally in your browser. No information stored on servers."}}, "back": "Back", "pages": {"home": {"meta": {"title": "4CV - AI-Powered Resume Analysis & Candidate Screening", "description": "Transform your hiring process with AI. Upload resumes and get instant, comprehensive candidate insights, skills assessment, and hiring recommendations."}, "features_title": "Why Choose 4CV?", "features_description": "Powerful AI technology designed to transform your hiring process.", "benefits_title": "Unlock Real Hiring Advantages", "benefits_subtitle": "Elevate recruitment with AI-powered insights for faster, smarter decisions.", "benefits": {"items": [{"title": "Save Time", "desc": "Analyze resumes in seconds instead of hours."}, {"title": "Better Decisions", "desc": "AI-powered insights help you make informed hiring choices."}, {"title": "Reduce <PERSON>", "desc": "Objective analysis based on skills and experience."}, {"title": "Scale Hiring", "desc": "Process hundreds of resumes efficiently."}], "ai_powered_title": "AI-Powered Analysis", "ai_powered_desc": "Advanced algorithms analyze 50+ criteria for comprehensive candidate insights."}, "howitworks_title": "How It Works", "howitworks_subtitle": "Get started with 4CV in just three simple steps.", "howitworks": {"steps": [{"title": "Upload Resume", "desc": "Simply drag and drop a PDF resume or click to select."}, {"title": "AI Analysis", "desc": "Our advanced AI analyzes skills, experience, and fit."}, {"title": "Get Insights", "desc": "Receive detailed analysis with scores and recommendations."}]}, "highlights_title": "Project Highlights", "highlights_subtitle": "Key features and capabilities of the 4CV platform.", "highlights": {"items": [{"title": "AI-Powered Analysis", "desc": "Advanced machine learning algorithms analyze resumes across 50+ criteria for comprehensive insights.", "metric": "50+ Criteria"}, {"title": "Privacy-First Design", "desc": "All processing happens locally in your browser. No resume data is stored on external servers.", "metric": "100% Private"}, {"title": "Multi-Language Support", "desc": "Available in English, Arabic, Spanish, and French, with more languages planned.", "metric": "4 Languages"}]}, "faq_title": "Frequently Asked Questions", "faq_subtitle": "Everything you need to know about 4CV.", "faq": {"items": [{"question": "How accurate is the AI analysis?", "answer": "Our AI has been trained on thousands of resumes and job descriptions, achieving 95%+ accuracy in skills identification and 90%+ accuracy in experience evaluation."}, {"question": "Is my data secure and private?", "answer": "Absolutely. All resume processing happens locally in your browser. We don't store any resume content on our servers, ensuring complete privacy."}, {"question": "What file formats are supported?", "answer": "Currently, we support PDF files up to 5MB in size. We're working on adding support for Word documents and other formats."}, {"question": "Can I integrate 4CV with my existing ATS?", "answer": "We're developing API integrations for popular ATS platforms. Contact us to discuss your specific integration needs."}, {"question": "How much does 4CV cost?", "answer": "4CV is currently free to use. We're committed to making AI-powered hiring accessible to organizations of all sizes."}]}, "cta_section": {"title": "Ready to Get Started?", "description": "Upload your first resume and experience the power of AI-driven analysis.", "primary": "Start Analyzing", "secondary": "Learn More"}}, "about": {"meta": {"title": "About 4CV - AI-Powered Resume Analysis Platform", "description": "Learn more about 4CV, the revolutionary AI-powered platform that transforms resume analysis and candidate screening for modern hiring teams."}, "content": {"markdown": "# Welcome to **4CV**\n\n> Transform your hiring process with intelligent resume analysis.\n\n---\n\n## **What is 4CV?**\n\n4CV is a revolutionary AI-powered platform designed to streamline and enhance your recruitment process. Our advanced technology analyzes resumes with unprecedented accuracy, providing you with actionable insights to make better hiring decisions.\n\n### **Key Features**\n\n- **AI-Powered Analysis** - Advanced machine learning algorithms\n- <u>Privacy-First Design</u> - All processing happens locally\n- <mark>Multi-Language Support</mark> - Available in 4 languages\n- ~~Manual Resume Screening~~ - Automated intelligent analysis\n- `Real-time Processing` - Get results in seconds\n\n## **Why Choose 4CV?**\n\n### **Core Advantages**\n\n1. **Lightning-Fast Analysis** - Process resumes in seconds, not hours\n2. <mark>Objective, Bias-Free Evaluation</mark> - AI removes human bias\n3. _Comprehensive Skill Assessment_ - 50+ criteria analysis\n4. <u>Complete Privacy Protection</u> - No data leaves your browser\n\n```javascript\n// Example: How 4CV processes your resume\nconst analysis = await analyzeResume(resumeFile);\nconsole.log(analysis.skills, analysis.experience, analysis.recommendations);\n```\n\n> \"4CV helped us reduce our screening time by 90% while improving candidate quality!\" — HR Director, Tech Startup\n\n---\n\n## **How It Works**\n\n### **Simple 3-Step Process**\n\n1. **Upload** - Drag and drop your PDF resume\n2. **Analyze** - Our AI processes the document instantly\n3. **Review** - Get detailed insights and recommendations\n\n### **Advanced Technology**\n\n- <u>Natural Language Processing</u> for skill extraction\n- <mark>Machine Learning models</mark> trained on thousands of resumes\n- **Pattern Recognition** for experience evaluation\n- _Contextual Analysis_ for role-specific insights\n\n---\n\n## **Privacy & Security**\n\n### **Your Data is Safe**\n\n- ✅ **Local Processing** - Everything happens in your browser\n- ✅ **No Data Storage** - We never save your resume content\n- ✅ **GDPR Compliant** - Full privacy protection\n- ✅ **Open Source** - Transparent and trustworthy\n\n---\n\n## **Get Started Today**\n\nReady to revolutionize your hiring process? Upload your first resume and experience the power of AI-driven analysis.\n\n**[Start Analyzing →](/upload)**"}}, "not_found": {"meta": {"title": "Page Not Found", "description": "The page you're looking for doesn't exist. Return to 4CV homepage for AI-powered resume analysis."}, "title": "404", "message": "Oops! Page not found", "return_home": "Return to Home", "go_back": "Go Back"}, "forbidden": {"meta": {"title": "Access Denied", "description": "You don't have permission to access this resource."}, "title": "Access Denied", "message": "You don't have permission to access this resource. Please contact support if you believe this is an error."}, "internal_server_error": {"meta": {"title": "Server Error", "description": "Something went wrong on our end. Please try again later."}, "title": "Server Error", "message": "Something went wrong on our end. Please try again later or contact support if the problem persists.", "refresh": "Refresh Page"}, "service_unavailable": {"meta": {"title": "Service Unavailable", "description": "The service is temporarily unavailable. Please try again later."}, "title": "Service Unavailable", "message": "We're currently performing maintenance or experiencing high traffic. Please try again in a few minutes.", "try_again": "Try Again"}, "history": {"meta": {"title": "Analysis History", "description": "View and manage your saved resume analyses and candidate insights"}, "title": "Analysis History", "description": "View and manage your saved resume analyses", "stats": {"total_analyses": "Total Analyses", "average_score": "Average Score", "this_week": "This Week", "top_candidates": "Top Candidates"}, "search_placeholder": "Search by filename or candidate name...", "sort": {"placeholder": "Sort by...", "date_newest": "Date (Newest First)", "date_oldest": "Date (Oldest First)", "score_highest": "Score (Highest First)", "score_lowest": "Score (Lowest First)", "name_az": "Name (A-Z)", "name_za": "Name (Z-A)"}, "actions": {"delete_all": "Delete All", "view": "View", "download": "Download", "delete": "Delete", "upload_resume": "Upload Resume"}, "empty": {"no_analyses": "No analyses yet", "no_matches": "No matching analyses found", "get_started": "Upload your first resume to get started", "adjust_search": "Try adjusting your search terms"}, "delete_confirm": {"title": "Delete Analysis", "message": "Are you sure you want to delete this analysis? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "delete_all_confirm": {"title": "Delete All Analyses", "message": "Are you sure you want to delete all {count} analyses? This action cannot be undone.", "cancel": "Cancel", "delete_all": "Delete All"}, "candidate": {"anonymous": "Anonymous Candidate", "analyzed": "Analyzed", "technical": "Technical", "experience": "Experience"}}}}