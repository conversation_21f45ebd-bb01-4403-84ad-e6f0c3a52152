{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "download": "Download", "upload": "Upload", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "clear": "Clear", "select_all": "Select All", "deselect_all": "Deselect All"}, "buttons": {"share": "Share", "download_report": "Download Report", "back": "Back", "back_to_top": "Back to top", "analyze_another": "Analyze Another Resume", "view_all_analyses": "View All Analyses", "upload_resume": "Upload Resume", "print_report": "Print Report", "html_report": "HTML Report", "json_data": "JSON Data", "raw_analysis_data": "Raw analysis data", "print_or_save_pdf": "Print or save as PDF"}, "notifications": {"file_uploaded": "Resume uploaded successfully", "analysis_complete": "Analysis completed successfully", "analysis_saved": "Analysis saved to dashboard", "analysis_deleted": "Analysis deleted successfully", "settings_saved": "Setting<PERSON> saved successfully", "api_key_saved": "API key configured successfully", "api_connection_success": "API connection successful", "api_connection_failed": "API connection failed", "data_cleared": "All data cleared successfully", "data_exported": "Data exported successfully", "upload_failed": "Upload failed", "link_copied": "Link copied to clipboard", "share_failed": "Sharing failed, link copied to clipboard instead", "share_not_supported": "Sharing not supported on this device"}, "toast": {"resume_uploaded": "Resume uploaded successfully", "extracted_characters": "Extracted {{count}} characters from {{filename}}"}, "analysis": {"loading": {"title": "Analyzing Resume", "complete": "Complete", "steps": {"parsing": "Parsing Document", "parsing_desc": "Extracting text and structure from your resume", "analyzing": "AI Analysis", "analyzing_desc": "Evaluating skills, experience, and qualifications", "generating": "Generating Report", "generating_desc": "Creating comprehensive insights and recommendations"}, "tip_title": "Did you know?", "tip_description": "Our AI analyzes over 50 different criteria to provide comprehensive candidate insights."}, "results": {"title": "Analysis Results", "anonymous_candidate": "Anonymous Candidate", "overall_score": "Overall Match Score", "score_breakdown": "Score Breakdown", "strengths": "Key Strengths", "areas_for_improvement": "Areas for Improvement", "skills": "Skills Identified", "experience": "Experience Summary", "years_experience": "Years of Experience", "companies": "Previous Companies", "recommendations": "Hiring Recommendations", "red_flags": "Potential Red Flags", "no_data_found": "No Analysis Data Found", "no_data_message": "Please upload a resume to see analysis results.", "formatted_web_page": "Formatted web page"}, "actions": {"download": "Download Report", "download_html": "Download HTML Report", "download_json": "Download JSON Data", "print": "Print Report", "share": "Share Analysis", "view": "View Analysis", "delete": "Delete Analysis", "analyze_another": "Analyze Another Resume", "back_to_upload": "Back to Upload"}, "categories": {"technical": "Technical Skills", "experience": "Experience Level", "communication": "Communication", "culture_fit": "Culture Fit"}, "labels": {"excellent": "Excellent", "good": "Good", "average": "Average", "below_average": "Below Average"}}, "upload_page": {"title": "Upload Resume", "description": "Upload a PDF resume to get instant AI-powered analysis and insights."}, "pages": {"home": {"features_title": "Key Features", "cta_section": {"title": "Ready to Transform Your Hiring Process?", "description": "Start analyzing resumes with AI-powered insights today."}}}, "shortcuts": {"home": "Go to Home", "upload": "Go to Upload", "history": "Go to History", "theme": "Toggle Theme", "language": "Toggle Language"}}