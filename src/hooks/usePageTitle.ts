import { useEffect } from 'react';
import { useLanguage } from './useLanguage';

/**
 * Custom hook for managing dynamic page titles with i18n support
 * @param titleKey - The translation key for the page title
 * @param suffix - Optional suffix to append to the title (defaults to "4CV")
 */
export const usePageTitle = (titleKey?: string, suffix: string = '4CV') => {
  const { t } = useLanguage();

  useEffect(() => {
    if (titleKey) {
      const translatedTitle = t(titleKey);
      document.title = `${translatedTitle} - ${suffix}`;
    } else {
      // Default title for home page
      const defaultTitle = t('pages.home.meta.title') || '4CV - AI-Powered Resume Analysis & Candidate Screening';
      document.title = defaultTitle;
    }
  }, [titleKey, suffix, t]);

  // Function to manually set title
  const setTitle = (title: string, includeSuffix: boolean = true) => {
    document.title = includeSuffix ? `${title} - ${suffix}` : title;
  };

  return { setTitle };
};
