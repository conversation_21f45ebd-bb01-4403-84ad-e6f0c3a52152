import { useHotkeys } from 'react-hotkeys-hook';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from './useLanguage';

/**
 * Custom hook for global keyboard shortcuts
 */
export const useKeyboardShortcuts = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  // Navigation shortcuts
  useHotkeys('ctrl+h, cmd+h', () => {
    navigate('/');
  }, {
    description: t('shortcuts.home') || 'Go to Home'
  });

  useHotkeys('ctrl+u, cmd+u', () => {
    navigate('/upload');
  }, {
    description: t('shortcuts.upload') || 'Go to Upload'
  });

  useHotkeys('ctrl+shift+h, cmd+shift+h', () => {
    navigate('/history');
  }, {
    description: t('shortcuts.history') || 'Go to History'
  });

  // Theme shortcuts
  useHotkeys('ctrl+shift+t, cmd+shift+t', () => {
    // This would need to be connected to your theme toggle
    const event = new CustomEvent('toggle-theme');
    window.dispatchEvent(event);
  }, {
    description: t('shortcuts.theme') || 'Toggle Theme'
  });

  // Language shortcuts
  useHotkeys('ctrl+shift+l, cmd+shift+l', () => {
    // This would need to be connected to your language toggle
    const event = new CustomEvent('toggle-language');
    window.dispatchEvent(event);
  }, {
    description: t('shortcuts.language') || 'Toggle Language'
  });

  return {
    shortcuts: [
      { key: 'Ctrl+H', description: t('shortcuts.home') || 'Go to Home' },
      { key: 'Ctrl+U', description: t('shortcuts.upload') || 'Go to Upload' },
      { key: 'Ctrl+Shift+H', description: t('shortcuts.history') || 'Go to History' },
      { key: 'Ctrl+Shift+T', description: t('shortcuts.theme') || 'Toggle Theme' },
      { key: 'Ctrl+Shift+L', description: t('shortcuts.language') || 'Toggle Language' },
    ]
  };
};
