import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollToTopOptions {
  behavior?: 'auto' | 'smooth';
  top?: number;
  left?: number;
  enabled?: boolean;
  delay?: number;
}

/**
 * Custom hook that automatically scrolls to the top of the page when the route changes
 * @param options - Configuration options for scroll behavior
 */
export const useScrollToTop = (options: ScrollToTopOptions = {}) => {
  const location = useLocation();
  
  const {
    behavior = 'smooth',
    top = 0,
    left = 0,
    enabled = true,
    delay = 0
  } = options;

  useEffect(() => {
    if (!enabled) return;

    const scrollToTop = () => {
      window.scrollTo({
        top,
        left,
        behavior
      });
    };

    if (delay > 0) {
      const timeoutId = setTimeout(scrollToTop, delay);
      return () => clearTimeout(timeoutId);
    } else {
      scrollToTop();
    }
  }, [location.pathname, behavior, top, left, enabled, delay]);
};

/**
 * Utility function to manually scroll to top
 * @param options - Scroll options
 */
export const scrollToTop = (options: ScrollToTopOptions = {}) => {
  const {
    behavior = 'smooth',
    top = 0,
    left = 0
  } = options;

  window.scrollTo({
    top,
    left,
    behavior
  });
};

/**
 * Utility function to scroll to a specific element
 * @param elementId - ID of the element to scroll to
 * @param options - Scroll options
 */
export const scrollToElement = (
  elementId: string, 
  options: ScrollToTopOptions & { offset?: number } = {}
) => {
  const {
    behavior = 'smooth',
    offset = 0
  } = options;

  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
      top: elementPosition,
      behavior
    });
  }
};

export default useScrollToTop;
