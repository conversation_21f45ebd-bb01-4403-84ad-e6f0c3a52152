import { useInView } from 'react-intersection-observer';
import { useAnimation } from 'framer-motion';
import { useEffect } from 'react';

/**
 * Custom hook for scroll-based animations using intersection observer
 * @param threshold - Percentage of element that needs to be visible (0-1)
 * @param triggerOnce - Whether to trigger animation only once
 */
export const useScrollAnimation = (threshold: number = 0.1, triggerOnce: boolean = true) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold,
    triggerOnce,
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    } else {
      controls.start('hidden');
    }
  }, [controls, inView]);

  return {
    ref,
    controls,
    inView,
    // Common animation variants
    variants: {
      hidden: { 
        opacity: 0, 
        y: 50,
        transition: { duration: 0.6 }
      },
      visible: { 
        opacity: 1, 
        y: 0,
        transition: { duration: 0.6, ease: 'easeOut' }
      }
    }
  };
};
