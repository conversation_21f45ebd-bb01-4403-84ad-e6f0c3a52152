import { useFontManager } from '@/hooks/useFontManager';
import FontManager, { FontConfig } from '@/utils/fontManager';
import { CookieStorage } from '@/utils/storage';
import { useTranslation } from 'react-i18next';

export const useLanguage = () => {
  const { i18n, t } = useTranslation();
  const { switchFont, currentFont, isLoading: isFontLoading } = useFontManager(i18n.language);

  const changeLanguage = async (language: string) => {
    try {
      // Change language and font simultaneously
      await Promise.all([
        i18n.changeLanguage(language),
        switchFont(language)
      ]);

      CookieStorage.setLanguage(language);

      // Update document attributes
      document.documentElement.lang = language;

      console.log(`🌍 Language changed to: ${language}`);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const getCurrentLanguage = () => i18n.language;
  
  const isRTL = () => {
    const rtlLanguages = ['ar', 'he', 'ur', 'fa'];
    return rtlLanguages.includes(i18n.language);
  };

  const getLanguageName = (code: string) => {
    const languageNames: { [key: string]: string } = {
      en: 'English',
      ar: 'العربية',
      es: 'Español',
      fr: 'Français'
    };
    return languageNames[code] || code;
  };

  const getSupportedLanguages = () => [
    { code: 'en', name: 'English', nativeName: 'English', countryCode: 'US' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية', countryCode: 'SA' },
    { code: 'es', name: 'Spanish', nativeName: 'Español', countryCode: 'ES' },
    { code: 'fr', name: 'French', nativeName: 'Français', countryCode: 'FR' }
  ];

  const getCurrentFont = (): FontConfig | null => {
    return FontManager.getFontConfig(getCurrentLanguage());
  };

  return {
    currentLanguage: getCurrentLanguage(),
    changeLanguage,
    isRTL: isRTL(),
    getLanguageName,
    getSupportedLanguages,
    // Font-related functionality
    currentFont,
    getCurrentFont,
    isFontLoading,
    t
  };
};
