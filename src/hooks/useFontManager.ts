import { useEffect, useState, useCallback } from 'react';
import Font<PERSON>anager, { FontConfig, LANGUAGE_FONTS } from '@/utils/fontManager';

interface UseFontManagerReturn {
  currentFont: FontConfig | null;
  isLoading: boolean;
  error: string | null;
  switchFont: (languageCode: string) => Promise<void>;
  preloadAllFonts: () => Promise<void>;
  isFontLoaded: (languageCode: string) => boolean;
}

/**
 * Custom hook for managing language-specific fonts
 */
export const useFontManager = (initialLanguage?: string): UseFontManagerReturn => {
  const [currentFont, setCurrentFont] = useState<FontConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Switch to a different language font
   */
  const switchFont = useCallback(async (languageCode: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      await FontManager.switchLanguageFont(languageCode);
      const fontConfig = FontManager.getFontConfig(languageCode);
      setCurrentFont(fontConfig);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to switch font';
      setError(errorMessage);
      console.error('Font switching error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Preload all language fonts
   */
  const preloadAllFonts = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      await FontManager.preloadAllFonts();
    } catch (err) {
      console.warn('Some fonts failed to preload:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Check if a specific language font is loaded
   */
  const isFontLoaded = useCallback((languageCode: string): boolean => {
    const fontConfig = FontManager.getFontConfig(languageCode);
    return fontConfig ? FontManager.isFontLoaded(fontConfig) : false;
  }, []);

  /**
   * Initialize font system on mount
   */
  useEffect(() => {
    const initializeFonts = async () => {
      if (initialLanguage) {
        await switchFont(initialLanguage);
      }
      // Preload other fonts in background
      preloadAllFonts();
    };

    initializeFonts();
  }, [initialLanguage, switchFont, preloadAllFonts]);

  return {
    currentFont,
    isLoading,
    error,
    switchFont,
    preloadAllFonts,
    isFontLoaded
  };
};

/**
 * Hook for getting font configuration without loading
 */
export const useFontConfig = (languageCode: string): FontConfig | null => {
  return LANGUAGE_FONTS[languageCode] || null;
};

/**
 * Hook for monitoring font loading status
 */
export const useFontLoadingStatus = () => {
  const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set());

  const checkFontStatus = useCallback(() => {
    const loaded = new Set<string>();
    Object.entries(LANGUAGE_FONTS).forEach(([lang, config]) => {
      if (FontManager.isFontLoaded(config)) {
        loaded.add(lang);
      }
    });
    setLoadedFonts(loaded);
  }, []);

  useEffect(() => {
    // Check initial status
    checkFontStatus();

    // Set up periodic checking (for development/debugging)
    const interval = setInterval(checkFontStatus, 1000);

    return () => clearInterval(interval);
  }, [checkFontStatus]);

  return {
    loadedFonts: Array.from(loadedFonts),
    isLanguageFontLoaded: (languageCode: string) => loadedFonts.has(languageCode),
    totalFontsLoaded: loadedFonts.size,
    totalFonts: Object.keys(LANGUAGE_FONTS).length
  };
};

export default useFontManager;
