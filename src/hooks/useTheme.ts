import { useState, useEffect } from 'react';
import { CookieStorage } from '@/utils/storage';

export type Theme = 'light' | 'dark';

export const useTheme = () => {
  const [theme, setThemeState] = useState<Theme>(() => {
    const savedTheme = CookieStorage.getTheme();
    // Convert auto to light as default if auto was previously saved
    return savedTheme === 'auto' ? 'light' : savedTheme;
  });

  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;

    // Remove existing theme classes
    root.classList.remove('light', 'dark');

    // Apply theme class
    if (newTheme === 'light') {
      root.classList.add('light');
    } else {
      root.classList.add('dark');
    }
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    CookieStorage.setTheme(newTheme);
    applyTheme(newTheme);
  };

  // Apply theme on mount and when theme changes
  useEffect(() => {
    applyTheme(theme);
  }, [theme]);

  return {
    theme,
    setTheme,
    effectiveTheme: theme,
    isLight: theme === 'light',
    isDark: theme === 'dark'
  };
};
