@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional AI Resume Screener Design System */

@layer base {
  :root {
    --brand-primary: 192 82% 44%;
    --brand-secondary: 184 28% 24%;
    --brand-tertiary: 35 92% 55%;
    --neutral-0: 0 0% 100%;
    --neutral-50: 200 12% 98%;
    --neutral-100: 198 10% 95%;
    --neutral-200: 196 10% 90%;
    --neutral-300: 194 10% 80%;
    --neutral-400: 192 8% 62%;
    --neutral-500: 190 10% 45%;
    --neutral-600: 188 12% 32%;
    --neutral-700: 186 16% 22%;
    --neutral-800: 184 20% 14%;
    --neutral-900: 184 24% 9%;
    --background: var(--neutral-50);
    --foreground: var(--neutral-900);
    --surface: var(--neutral-0);
    --surface-variant: var(--neutral-100);
    --headline: var(--neutral-900);
    --body-text: var(--neutral-600);
    --caption: var(--neutral-400);
    --disabled: 196 8% 86%;
    --primary: var(--brand-primary);
    --primary-foreground: 197 100% 12%;
    --primary-hover: 192 82% 40%;
    --primary-active: 192 82% 36%;
    --secondary: var(--brand-secondary);
    --secondary-foreground: 0 0% 100%;
    --secondary-hover: 184 28% 20%;
    --secondary-active: 184 28% 16%;
    --accent: var(--brand-tertiary);
    --accent-foreground: 210 28% 12%;
    --accent-hover: 35 92% 50%;
    --accent-active: 35 92% 44%;
    --success: 151 60% 40%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 52%;
    --warning-foreground: 0 0% 100%;
    --error: 2 78% 58%;
    --error-foreground: 0 0% 100%;
    --info: 201 92% 46%;
    --info-foreground: 0 0% 100%;
    --card: var(--surface);
    --card-foreground: var(--foreground);
    --border: var(--neutral-200);
    --input: var(--neutral-200);
    --ring: var(--primary);
    --radius: 0.75rem;
    --overlay: 0 0% 0%;
    --modal-background: var(--background);
    --gradient-primary: linear-gradient(135deg, hsl(var(--brand-secondary)), hsl(var(--brand-primary)));
    --gradient-surface: linear-gradient(180deg, hsl(var(--surface)), hsl(var(--background)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-primary) / 0.18), hsl(var(--brand-tertiary) / 0.18));
    --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.06);
    --shadow-base: 0 2px 6px 0 hsl(0 0% 0% / 0.08), 0 1px 2px 0 hsl(0 0% 0% / 0.06);
    --shadow-lg: 0 12px 20px -3px hsl(0 0% 0% / 0.12), 0 6px 10px -2px hsl(0 0% 0% / 0.08);
    --shadow-xl: 0 24px 35px -8px hsl(0 0% 0% / 0.18), 0 10px 12px -6px hsl(0 0% 0% / 0.10);
    --sidebar-background: var(--surface);
    --sidebar-foreground: var(--body-text);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--surface-variant);
    --sidebar-accent-foreground: var(--body-text);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
    --transition-fast: 150ms cubic-bezier(0.2, 0, 0.2, 1);
    --transition-base: 220ms cubic-bezier(0.2, 0, 0.2, 1);
    --transition-slow: 320ms cubic-bezier(0.2, 0, 0.2, 1);
    --white: 0 0% 100%;
    --white-alpha-10: 0 0% 100% / 0.1;
    --logo-blue: 201 16% 92%;
    --logo-react: 192 82% 44%;
    --muted-text: var(--neutral-500);
  }

  .dark {
    --background: 190 28% 10%;
    --foreground: 0 0% 100%;
    --surface: 192 26% 12%;
    --surface-variant: 190 22% 16%;
    --headline: 0 0% 100%;
    --body-text: 198 18% 82%;
    --caption: 198 14% 72%;
    --disabled: 192 12% 22%;
    --primary: 192 82% 52%;
    --primary-foreground: 197 100% 10%;
    --primary-hover: 192 82% 48%;
    --primary-active: 192 82% 44%;
    --secondary: 184 28% 28%;
    --secondary-foreground: 0 0% 100%;
    --secondary-hover: 184 28% 24%;
    --secondary-active: 184 28% 20%;
    --accent: 35 92% 58%;
    --accent-foreground: 210 28% 10%;
    --accent-hover: 35 92% 54%;
    --accent-active: 35 92% 48%;
    --success: 151 60% 46%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 58%;
    --warning-foreground: 210 28% 10%;
    --error: 2 78% 64%;
    --error-foreground: 0 0% 100%;
    --info: 201 92% 54%;
    --info-foreground: 0 0% 100%;
    --card: var(--surface);
    --card-foreground: var(--foreground);
    --border: 0 0% 100% / 0.14;
    --input: 190 18% 20%;
    --ring: var(--primary);
    --gradient-surface: linear-gradient(180deg, hsl(var(--surface)), hsl(var(--background)));
    --gradient-hero: linear-gradient(135deg, hsl(201 16% 92% / 0.12), hsl(192 82% 52% / 0.14));
    --sidebar-background: var(--surface);
    --sidebar-foreground: var(--body-text);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--surface-variant);
    --sidebar-accent-foreground: var(--body-text);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }

}


@layer base {
  * {
    @apply border-border;
  }

  /* Language-specific font support */
  :root {
    /* Default font families for each language */
    --font-family-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-fr: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-es: 'Nunito Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-ar: 'Cairo', Tahoma, Arial, sans-serif;

    /* Current active font family */
    --font-family-current: var(--font-family-en);

    /* Text direction support */
    --text-direction: ltr;
  }

  /* Apply current font family and styles to body */
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family-current);
    direction: var(--text-direction);
  }

  /* Ensure font inheritance */
  * {
    font-family: inherit;
  }

  /* Smooth font transitions */
  body,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  span,
  div,
  button,
  input,
  textarea {
    transition: font-family 0.3s ease-in-out;
  }

  /* RTL language support */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  /* Font loading optimization */
  @supports (font-display: swap) {
    @font-face {
      font-display: swap;
    }
  }
}



.text-pop-up-top {
  -webkit-animation: text-pop-up-top 500ms ease-out both;
  animation: text-pop-up-top 500ms ease-out both;
}


@-webkit-keyframes text-pop-up-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    text-shadow: none;
  }

  100% {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    text-shadow: 0 1px 0 #cccccc, 0 2px 0 #cccccc, 0 3px 0 #cccccc, 0 4px 0 #cccccc, 0 5px 0 #cccccc, 0 6px 0 #cccccc, 0 7px 0 #cccccc, 0 8px 0 #cccccc, 0 9px 0 #cccccc, 0 50px 30px rgba(0, 0, 0, 0.3);
  }
}

@keyframes text-pop-up-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    text-shadow: none;
  }

  100% {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    text-shadow: 0 1px 0 #cccccc, 0 2px 0 #cccccc, 0 3px 0 #cccccc, 0 4px 0 #cccccc, 0 5px 0 #cccccc, 0 6px 0 #cccccc, 0 7px 0 #cccccc, 0 8px 0 #cccccc, 0 9px 0 #cccccc, 0 50px 30px rgba(0, 0, 0, 0.3);
  }
}


button,
button:hover {
  direction: ltr !important;
  transition: all 0.3s ease 0s !important;
}


.test {
  @apply bg-green-500 text-black border-[5px] border-black
}

.smooth-hover, .smooth-hover:hover {
  transition: all 0.3s ease 0s !important;
}


.section{
  @apply py-20
}
