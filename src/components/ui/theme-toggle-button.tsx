"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Expand } from "@theme-toggles/react";
import "@theme-toggles/react/css/Expand.css";
import { useTheme } from "next-themes";
import React from "react";

import {
  AnimationStart,
  AnimationVariant,
  createAnimation,
} from "@/themes/theme-animations";

interface ThemeToggleAnimationProps {
  variant?: AnimationVariant;
  start?: AnimationStart;
  showLabel?: boolean;
  url?: string;
}

export function ThemeToggleButton({
  variant = "circle-blur",
  start = "top-right",
  showLabel = false,
  url = "",
}: ThemeToggleAnimationProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();

  // Avoid SSR/CSR mismatch for icon and theme-dependent UI
  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => setMounted(true), []);

  // Effective theme (uses resolvedTheme to handle 'system')
  const effectiveTheme = resolvedTheme || theme;

  // Drive icon deterministically: light => false, dark => true
  const derivedToggled = effectiveTheme === "dark";
  const [iconToggled, setIconToggled] = React.useState<boolean>(derivedToggled);
  React.useEffect(() => setIconToggled(derivedToggled), [derivedToggled]);

  // Single dynamic style tag for animation CSS
  const styleId = "theme-transition-styles";
  const updateStyles = React.useCallback((css: string) => {
    if (typeof window === "undefined") return;
    let styleElement = document.getElementById(styleId) as HTMLStyleElement | null;
    if (!styleElement) {
      styleElement = document.createElement("style");
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }
    styleElement.textContent = css;
  }, []);

  // Switch theme and sync icon immediately
  const switchTheme = React.useCallback(() => {
    const next = effectiveTheme === "light" ? "dark" : "light";
    setTheme(next);
    setIconToggled(next === "dark");
  }, [effectiveTheme, setTheme]);

  // Toggle with View Transition fallback
  const toggleTheme = React.useCallback(() => {
    const animation = createAnimation(variant, start, url);
    updateStyles(animation.css);

    if (typeof document === "undefined") {
      switchTheme();
      return;
    }

    if (!("startViewTransition" in document)) {
      switchTheme();
      return;
    }

    // @ts-expect-error experimental API not yet in TS DOM lib
    document.startViewTransition(() => {
      switchTheme();
    });
  }, [variant, start, url, updateStyles, switchTheme]);

  if (!mounted) {
    // Preserve layout without interactive icon pre-mount
    return (
      <Button
        variant="ghost"
        size="icon"
        className="w-9 h-9 p-0 relative group bg-transparent hover:bg-transparent"
        aria-label="Toggle theme"
        name="Theme Toggle Button"
        disabled
      >
        <span className="block w-5 h-5" />
      </Button>
    );
  }

  // Use precise typing from the component and add an empty spread to defuse TS false positives
  const expandProps: React.ComponentProps<typeof Expand> = {
    duration: 750,
    toggled: iconToggled,
    toggle: setIconToggled,
    onToggle: (t) => {
      const targetTheme = t ? "dark" : "light";
      if (targetTheme !== effectiveTheme) {
        // Route through the same transition logic for consistency
        toggleTheme();
      }
    },
  };

  return (
    <Button
      onClick={toggleTheme}
      variant="ghost"
      size="icon"
      className="w-9 p-0 h-9 relative group bg-transparent hover:bg-transparent hover:text-headline"
      name="Theme Toggle Button"
      aria-label="Toggle theme"
      title="Toggle theme"
    >
      {/* The empty spread avoids bogus "missing optional DOM props" errors in TS */}
      <Expand {...{}} {...expandProps} />

      {showLabel && (
        <span className="sr-only">
          {effectiveTheme === "dark" ? "Switch to light theme" : "Switch to dark theme"}
        </span>
      )}
    </Button>
  );
}
