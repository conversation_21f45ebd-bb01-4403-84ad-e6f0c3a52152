import CookieConsent from '@/components/common/CookieConsent';
import { Toaster } from '@/components/ui/toaster';
import { motion } from 'framer-motion';
import React from 'react';
import { useLocation } from 'react-router-dom';
import Footer from './Footer';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

// List all valid routes here
const VALID_PATHS = [
  '/', '/about', '/pricing', '/contact', '/faq', '/upload', '/history'
];

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  // If path is NOT in valid list, we consider it NotFound:
  const isNotFound = !VALID_PATHS.includes(location.pathname);

  return (
    <div className="min-h-screen bg-background text-foreground">
      {!isNotFound && <Header />}

      <main className="flex-1">
        <motion.div
          key={location.pathname}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="min-h-[calc(100vh-5rem)]"
        >
          {children}
        </motion.div>
      </main>

      {!isNotFound && <Footer />}

      <Toaster />
      <CookieConsent />



      {/* Background decoration */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 sm:-top-40 -right-20 sm:-right-40 w-40 sm:w-80 h-40 sm:h-80 rounded-full bg-gradient-primary opacity-10 sm:opacity-20 blur-2xl sm:blur-3xl" />
        <div className="absolute -bottom-20 sm:-bottom-40 -left-20 sm:-left-40 w-40 sm:w-80 h-40 sm:h-80 rounded-full bg-gradient-primary opacity-10 sm:opacity-20 blur-2xl sm:blur-3xl" />
      </div>
    </div>
  );
};

export default Layout;
