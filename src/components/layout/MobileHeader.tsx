// src/components/headers/MobileHeader.tsx
import LanguageSelector from "@/components/common/LanguageSelector";
import { ThemeToggleButton } from "@/components/ui/theme-toggle-button";
import { useLanguage } from "@/hooks/useLanguage";
import { navigateAndScrollTop } from "@/utils/helper";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { IoClose, IoMenu } from "react-icons/io5";
import { useLocation, useNavigate } from "react-router-dom";

const NAV_ITEMS = [
  { id: "home", label: (t: any) => t("navigation.home"), href: "/" },
  { id: "upload", label: (t: any) => t("navigation.upload"), href: "/upload" },
  { id: "history", label: (t: any) => t("navigation.history"), href: "/history" },
];

export default function MobileHeader() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const [open, setOpen] = useState(false);

  const navItems = NAV_ITEMS.map((item) => ({
    ...item,
    label: item.label(t),
  }));

  const isActive = (href: string) =>
    href === "/" ? location.pathname === "/" : location.pathname.startsWith(href);

  const handleNavigation = (href: string) => {
    navigateAndScrollTop(navigate, location.pathname, href, "smooth");
    setOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 w-full bg-background border-b border-border shadow-sm backdrop-blur-xl lg:hidden">
      <div className="flex items-center justify-between h-16 px-4">
        <button
          onClick={() => handleNavigation("/")}
          className="font-bold text-xl text-primary"
          aria-label={t('navigation.home_aria')}
          type="button"
        >
          4CV
        </button>

        <div className="flex items-center gap-2">
          <ThemeToggleButton />
          <button
            onClick={() => setOpen(true)}
            className="flex items-center justify-center w-10 h-10 rounded-md border border-border hover:bg-surface/60 transition"
            aria-label={t('navigation.open_menu')}
            type="button"
          >
            <IoMenu size={22} className="text-primary" />
          </button>
        </div>
      </div>

      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-[80]" onClose={setOpen}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-out duration-200"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-in duration-150"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/40 backdrop-blur-sm" />
          </Transition.Child>

          {/* Drawer Side-Panel */}
          <div className="fixed inset-0 flex justify-end">
            <Transition.Child
              as={Fragment}
              enter=" transition opacity-[40%] ease-in-out duration-300"
              enterFrom="translate-x-0 opacity-[40%]"
              enterTo="translate-x-0 opacity-[40%]"
              leave=" transition opacity-[40%] ease-in-out duration-200"
              leaveFrom="translate-x-0 opacity-[40%]"
              leaveTo="translate-x-0 opacity-[40%]"
            >
              <Dialog.Panel className=" w-[100vw]  h-full bg-background border-l border-border shadow-lg flex flex-col focus:outline-none">
                {/* Drawer Header */}
                <div className="flex items-center justify-between px-5 pt-6 pb-4 border-b border-border">
                  <span className="font-bold text-primary text-xl">4CV</span>
                  <button
                    className="p-2 rounded hover:bg-surface transition"
                    onClick={() => setOpen(false)}
                    aria-label={t('navigation.close_menu')}
                    type="button"
                  >
                    <IoClose size={26} />
                  </button>
                </div>

                {/* Nav Links */}
                <nav className="flex flex-col gap-2 px-5 py-6 items-center justify-center text-center flex-1">
                  {navItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => handleNavigation(item.href)}
                      className={`w-full text-center py-3 px-3 rounded-lg text-base font-medium focus:outline-none focus-visible:ring focus-visible:ring-primary/70 transition
                        ${isActive(item.href)
                          ? " text-headline"
                          : "text-body-text"
                        }`}
                      aria-current={isActive(item.href) ? "page" : undefined}
                      type="button"
                    >
                      {item.label}
                    </button>
                  ))}
                </nav>

                {/* Controls */}
                <div className="border-t border-border px-5 py-6 bg-background/80">
                  <div className="flex items-center justify-center gap-5">
                    <LanguageSelector />
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>
    </header>
  );
}
