import { useLanguage } from '@/hooks/useLanguage';
import { motion } from 'framer-motion';
import React from 'react';
import {
  IoAnalytics,
  IoArrowUp,
  IoCloudUpload,
  IoDocument,
  IoGlobe,
  IoHelp,
  IoShield,
  IoTime
} from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const footerSections = [
    {
      title: t('footer.sections.product'),
      links: [
        { label: t('footer.links.features'), href: '/#features', icon: <IoAnalytics className="w-4 h-4" /> },
        { label: t('footer.links.benefits'), href: '/#benefits', icon: <IoShield className="w-4 h-4" /> },
        { label: t('footer.links.how_it_works'), href: '/#how-it-works', icon: <IoGlobe className="w-4 h-4" /> },
      ]
    },
    {
      title: t('footer.sections.application'),
      links: [
        { label: t('footer.links.upload'), href: '/upload', icon: <IoCloudUpload className="w-4 h-4" /> },
        { label: t('footer.links.history'), href: '/history', icon: <IoTime className="w-4 h-4" /> },
        { label: t('footer.links.about'), href: '/learn-more', icon: <IoDocument className="w-4 h-4" /> },
      ]
    },
    {
      title: t('footer.sections.help'),
      links: [
        { label: t('footer.links.faq'), href: '/#faq', icon: <IoHelp className="w-4 h-4" /> },
        { label: t('footer.links.get_started'), href: '/upload', icon: <IoArrowUp className="w-4 h-4" /> },
      ]
    }
  ];

  const handleLinkClick = (href: string) => {
    if (href.startsWith('/#')) {
      // Handle anchor links
      navigate('/');
      setTimeout(() => {
        const element = document.querySelector(href.substring(1));
        element?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } else if (href.startsWith('/')) {
      navigate(href);
    } else {
      window.open(href, '_blank', 'noopener,noreferrer');
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="bg-gradient-to-br from-surface/50 to-surface/30 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
            {/* Brand Section */}
            <motion.div
              className="lg:col-span-2"
              initial={{ opacity: 0, y: 0 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="mb-6">
                <button
                  onClick={() => navigate('/')}
                  className="text-3xl font-bold text-primary hover:text-primary-hover transition-colors"
                >
                  4CV
                </button>
                <p className="text-sm text-primary/80 mt-1 font-medium">
                  {t('footer.tagline')}
                </p>
              </div>

              <p className="text-body-text leading-relaxed mb-6 max-w-md">
                {t('footer.description')}
              </p>


            </motion.div>

            {/* Footer Links */}
            {footerSections.map((section, index) => (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, y: 0 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-headline font-semibold mb-4 text-sm uppercase tracking-wider">
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.label}>
                      <button
                        onClick={() => handleLinkClick(link.href)}
                        className="group flex items-center space-x-2 text-body-text hover:text-primary transition-colors text-sm"
                      >
                        <span className="text-caption group-hover:text-primary transition-colors">
                          {link.icon}
                        </span>
                        <span>{link.label}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom Bar */}
        <div
          dir='ltr'
          className="border-t border-border py-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0"
          >
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-caption text-sm">
              <div className="flex items-center space-x-2">
                <span>© {currentYear} 4CV.</span>
                <span className="text-caption/60">•</span>
                <span className="text-caption/80">{t('footer.tagline')}</span>
              </div>
            </div>

            {/* Scroll to top button */}
            <motion.button
              onClick={scrollToTop}
              className="flex items-center space-x-2 text-caption hover:text-primary transition-colors text-sm group"
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="hidden sm:inline">{t('buttons.back_to_top')}</span>
              <IoArrowUp className="w-4 h-4 group-hover:text-primary transition-colors" />
            </motion.button>

          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
