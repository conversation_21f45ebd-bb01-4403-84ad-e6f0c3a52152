// src/components/headers/DesktopHeader.tsx
import LanguageSelector from "@/components/common/LanguageSelector";
import { ThemeToggleButton } from "@/components/ui/theme-toggle-button";
import { useLanguage } from "@/hooks/useLanguage";
import { navigateAndScrollTop } from "@/utils/helper";
import { useLocation, useNavigate } from "react-router-dom";

const NAV_ITEMS = [
  { id: "home", label: (t: any) => t("navigation.home"), href: "/" },
  { id: "upload", label: (t: any) => t("navigation.upload"), href: "/upload" },
  { id: "history", label: (t: any) => t("navigation.history"), href: "/history" },
];

export default function DesktopHeader() {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = NAV_ITEMS.map((item) => ({
    ...item,
    label: item.label(t),
  }));

  const isActive = (href: string) =>
    href === "/" ? location.pathname === "/" : location.pathname.startsWith(href);

  const handleNavigation = (href: string) => {
    navigateAndScrollTop(navigate, location.pathname, href, "smooth");
  };

  return (
    <header
      dir="ltr"
      className="sticky top-0 z-40 w-full bg-background border-b border-border shadow-sm backdrop-blur-xl"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex h-20 items-center justify-between">
        {/* Logo */}
        <button
          className="flex items-center min-w-0 hover:opacity-80 transition-opacity"
          onClick={() => handleNavigation("/")}
          aria-label={t("navigation.home_aria")}
        >
          <h1 className="text-2xl font-bold text-primary tracking-tight">4CV</h1>
        </button>

        {/* Nav */}
        <nav className="flex items-center space-x-8 ms-[200px]">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => handleNavigation(item.href)}
              className={`px-0 py-2 text-sm font-medium transition-all ${isActive(item.href)
                  ? "text-primary border-b-2 border-primary"
                  : "text-body-text hover:text-headline"
                }`}
              aria-current={isActive(item.href) ? "page" : undefined}
            >
              {item.label}
            </button>
          ))}
        </nav>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          <ThemeToggleButton />
          <LanguageSelector />
        </div>
      </div>
    </header>
  );
}
