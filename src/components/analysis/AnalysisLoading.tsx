import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import Card from '@/components/common/Card';
import { IoAnalytics, IoDocument, IoFlash, IoCheckmark } from 'react-icons/io5';

interface AnalysisLoadingProps {
  fileName: string;
  onCancel?: () => void;
}

const AnalysisLoading: React.FC<AnalysisLoadingProps> = ({ fileName, onCancel }) => {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps = [
    {
      id: 'parsing',
      icon: IoDocument,
      title: t('analysis.loading.steps.parsing'),
      description: t('analysis.loading.steps.parsing_desc'),
      duration: 2000
    },
    {
      id: 'analyzing',
      icon: IoFlash,
      title: t('analysis.loading.steps.analyzing'),
      description: t('analysis.loading.steps.analyzing_desc'),
      duration: 8000
    },
    {
      id: 'generating',
      icon: IoAnalytics,
      title: t('analysis.loading.steps.generating'),
      description: t('analysis.loading.steps.generating_desc'),
      duration: 3000
    }
  ];

  useEffect(() => {
    const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);
    let elapsed = 0;

    const interval = setInterval(() => {
      elapsed += 100;
      const newProgress = Math.min((elapsed / totalDuration) * 100, 100);
      setProgress(newProgress);

      // Update current step based on progress
      let stepProgress = 0;
      for (let i = 0; i < steps.length; i++) {
        stepProgress += (steps[i].duration / totalDuration) * 100;
        if (newProgress <= stepProgress) {
          setCurrentStep(i);
          break;
        }
      }

      if (elapsed >= totalDuration) {
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-md"
      >
        <Card variant="elevated" padding="lg" className="text-center">
          <div className="space-y-6">
            {/* Header */}
            <div>
        
              <h3 className="text-xl font-bold text-headline mb-2">
                {t('analysis.loading.title')}
              </h3>
              <p className="text-sm text-caption">
                {fileName}
              </p>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="w-full bg-surface rounded-full h-2">
                <motion.div
                  className="h-2 bg-gradient-primary rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.1 }}
                />
              </div>
              <p className="text-sm text-caption">
                {Math.round(progress)}% {t('analysis.loading.complete')}
              </p>
            </div>

            {/* Steps */}
            <div className="space-y-4">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = index === currentStep;
                const isCompleted = index < currentStep;
                
                return (
                  <motion.div
                    key={step.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                      isActive 
                        ? 'bg-primary/10 border border-primary/20' 
                        : isCompleted 
                        ? 'bg-success/10' 
                        : 'bg-surface/50'
                    }`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isActive 
                        ? 'bg-primary text-white' 
                        : isCompleted 
                        ? 'bg-success text-white' 
                        : 'bg-surface text-caption'
                    }`}>
                      {isCompleted ? (
                        <IoCheckmark className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <h4 className={`text-sm font-medium ${
                        isActive ? 'text-primary' : isCompleted ? 'text-success' : 'text-headline'
                      }`}>
                        {step.title}
                      </h4>
                      <p className="text-xs text-caption">
                        {step.description}
                      </p>
                    </div>
                    {isActive && (
                      <motion.div
                        className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                    )}
                  </motion.div>
                );
              })}
            </div>

            {/* Tips */}
            <div className="bg-surface/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-headline mb-2">
                {t('analysis.loading.tip_title')}
              </h4>
              <p className="text-xs text-caption">
                {t('analysis.loading.tip_description')}
              </p>
            </div>

            {/* Cancel Button */}
            {onCancel && (
              <button
                onClick={onCancel}
                className="text-sm text-caption hover:text-body-text transition-colors"
              >
                {t('common.cancel')}
              </button>
            )}
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default AnalysisLoading;
