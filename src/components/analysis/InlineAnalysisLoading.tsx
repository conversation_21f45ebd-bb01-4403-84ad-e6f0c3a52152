import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import Card from '@/components/common/Card';
import { IoAnalytics, IoDocument, IoFlash, IoCheckmark } from 'react-icons/io5';

interface InlineAnalysisLoadingProps {
  fileName: string;
  onCancel?: () => void;
}

const InlineAnalysisLoading: React.FC<InlineAnalysisLoadingProps> = ({ fileName, onCancel }) => {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps = [
    {
      id: 'parsing',
      icon: IoDocument,
      title: t('analysis.loading.steps.parsing'),
      description: t('analysis.loading.steps.parsing_desc'),
      duration: 2000
    },
    {
      id: 'analyzing',
      icon: IoFlash,
      title: t('analysis.loading.steps.analyzing'),
      description: t('analysis.loading.steps.analyzing_desc'),
      duration: 8000
    },
    {
      id: 'generating',
      icon: IoAnalytics,
      title: t('analysis.loading.steps.generating'),
      description: t('analysis.loading.steps.generating_desc'),
      duration: 3000
    }
  ];

  useEffect(() => {
    const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);
    let elapsed = 0;

    const interval = setInterval(() => {
      elapsed += 100;
      const newProgress = Math.min((elapsed / totalDuration) * 100, 100);
      setProgress(newProgress);

      // Update current step based on progress
      let stepProgress = 0;
      for (let i = 0; i < steps.length; i++) {
        stepProgress += (steps[i].duration / totalDuration) * 100;
        if (newProgress <= stepProgress) {
          setCurrentStep(i);
          break;
        }
      }

      if (elapsed >= totalDuration) {
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="w-full"
    >
      <Card variant="elevated" padding="xl" className="text-center">
        <div className="space-y-8">
          {/* Header */}
          <div>
            <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-primary flex items-center justify-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <IoAnalytics className="w-10 h-10 text-white" />
              </motion.div>
            </div>
            <h3 className="text-2xl font-bold text-headline mb-3">
              {t('analysis.loading.title')}
            </h3>
            <p className="text-body-text">
              {fileName}
            </p>
          </div>

          {/* Progress Bar */}
          <div className="space-y-3">
            <div className="w-full bg-surface rounded-full h-3">
              <motion.div
                className="h-3 bg-gradient-primary rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
            <p className="text-lg font-medium text-primary">
              {Math.round(progress)}% {t('analysis.loading.complete')}
            </p>
          </div>

          {/* Steps */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <motion.div
                  key={step.id}
                  className={`p-6 rounded-xl transition-all duration-300 ${
                    isActive 
                      ? 'bg-primary/10 border-2 border-primary/30 scale-105' 
                      : isCompleted 
                      ? 'bg-success/10 border-2 border-success/30' 
                      : 'bg-surface/50 border-2 border-border/30'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    isActive 
                      ? 'bg-primary text-white' 
                      : isCompleted 
                      ? 'bg-success text-white' 
                      : 'bg-surface text-caption'
                  }`}>
                    {isCompleted ? (
                      <IoCheckmark className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  <h4 className={`text-lg font-semibold mb-2 ${
                    isActive ? 'text-primary' : isCompleted ? 'text-success' : 'text-headline'
                  }`}>
                    {step.title}
                  </h4>
                  <p className="text-sm text-caption">
                    {step.description}
                  </p>
                  {isActive && (
                    <motion.div
                      className="mt-4 w-6 h-6 mx-auto border-2 border-primary border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  )}
                </motion.div>
              );
            })}
          </div>

          {/* Tips */}
          <div className="bg-surface/50 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-headline mb-3">
              {t('analysis.loading.tip_title')}
            </h4>
            <p className="text-body-text">
              {t('analysis.loading.tip_description')}
            </p>
          </div>

          {/* Cancel Button */}
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-caption hover:text-body-text transition-colors"
            >
              {t('common.cancel')}
            </button>
          )}
        </div>
      </Card>
    </motion.div>
  );
};

export default InlineAnalysisLoading;
