import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import { useLanguage } from '@/hooks/useLanguage';
import { GeminiAnalysisResponse } from '@/utils/gemini';
import { motion } from 'framer-motion';
import React from 'react';
import {
  IoCheckmark,
  IoDownload,
  IoShare,
  IoStar,
  IoTrendingUp,
  IoWarning
} from 'react-icons/io5';

interface AnalysisResultsProps {
  analysis: GeminiAnalysisResponse;
  fileName: string;
  onClose?: () => void;
  onSave?: () => void;
  onDownload?: () => void;
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({
  analysis,
  fileName,
  onClose,
  onSave,
  onDownload
}) => {
  const { t } = useLanguage();

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-success';
    if (score >= 60) return 'text-warning';
    return 'text-error';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return t('analysis.labels.excellent');
    if (score >= 60) return t('analysis.labels.good');
    if (score >= 40) return t('analysis.labels.average');
    return t('analysis.labels.below_average');
  };

  const ScoreCard = ({ title, score, icon }: { title: string; score: number; icon: React.ReactNode }) => (
    <Card variant="elevated" padding="md" className="text-center">
      <div className="flex flex-col items-center space-y-2">
        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
          {icon}
        </div>
        <h4 className="text-sm font-medium text-caption">{title}</h4>
        <div className="space-y-1">
          <p className={`text-2xl font-bold ${getScoreColor(score)}`}>{score}%</p>
          <p className="text-xs text-caption">{getScoreLabel(score)}</p>
        </div>
      </div>
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-6xl mx-auto"
    >
      {/* Header */}
      <Card variant="elevated" padding="lg" className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h2 className="text-3xl font-bold text-headline">
              {t('analysis.results.title')}
            </h2>
            <p className="text-body-text mt-2">
              <span className="font-medium">{fileName}</span>
              {analysis.candidateName && (
                <span className="text-caption"> • {analysis.candidateName}</span>
              )}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
            {onDownload && (
              <Button
                variant="outline"
                size="md"
                onClick={onDownload}
                leftIcon={<IoDownload />}
                className="w-full sm:w-auto"
              >
                {t('analysis.actions.download')}
              </Button>
            )}
            {onSave && (
              <Button
                variant="primary"
                size="md"
                onClick={onSave}
                leftIcon={<IoCheckmark />}
                className="w-full sm:w-auto"
              >
                {t('analysis.actions.save')}
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Content */}
      <div className="space-y-8">
        {/* Overall Score */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-primary mb-4">
            <span className="text-3xl font-bold text-white">{analysis.overallScore}%</span>
          </div>
          <h3 className="text-xl font-semibold text-headline mb-2">
            {t('analysis.results.overall_score')}
          </h3>
          <p className="text-body-text max-w-2xl mx-auto">
            {analysis.summary}
          </p>
        </div>

        {/* Score Breakdown */}
        <div>
          <h3 className="text-lg font-semibold text-headline mb-4">
            {t('analysis.results.score_breakdown')}
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <ScoreCard
              title={t('analysis.categories.technical')}
              score={analysis.scores.technical}
              icon={<IoTrendingUp className="w-5 h-5 text-primary" />}
            />
            <ScoreCard
              title={t('analysis.categories.experience')}
              score={analysis.scores.experience}
              icon={<IoStar className="w-5 h-5 text-primary" />}
            />
            <ScoreCard
              title={t('analysis.categories.communication')}
              score={analysis.scores.communication}
              icon={<IoShare className="w-5 h-5 text-primary" />}
            />
            <ScoreCard
              title={t('analysis.categories.culture_fit')}
              score={analysis.scores.cultureFit}
              icon={<IoCheckmark className="w-5 h-5 text-primary" />}
            />
          </div>
        </div>

        {/* Strengths & Weaknesses */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card variant="elevated" padding="lg">
            <h4 className="text-lg font-semibold text-headline mb-4 flex items-center">
              <IoCheckmark className="w-5 h-5 text-success mr-2" />
              {t('analysis.results.strengths')}
            </h4>
            <ul className="space-y-2">
              {analysis.strengths.map((strength, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 rounded-full bg-success mt-2 flex-shrink-0" />
                  <span className="text-body-text">{strength}</span>
                </li>
              ))}
            </ul>
          </Card>

          <Card variant="elevated" padding="lg">
            <h4 className="text-lg font-semibold text-headline mb-4 flex items-center">
              <IoWarning className="w-5 h-5 text-warning mr-2" />
              {t('analysis.results.areas_for_improvement')}
            </h4>
            <ul className="space-y-2">
              {analysis.weaknesses.map((weakness, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 rounded-full bg-warning mt-2 flex-shrink-0" />
                  <span className="text-body-text">{weakness}</span>
                </li>
              ))}
            </ul>
          </Card>
        </div>

        {/* Skills & Experience */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card variant="elevated" padding="lg">
            <h4 className="text-lg font-semibold text-headline mb-4">
              {t('analysis.results.skills')}
            </h4>
            <div className="flex flex-wrap gap-2">
              {analysis.skills.map((skill, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                >
                  {skill}
                </span>
              ))}
            </div>
          </Card>

          <Card variant="elevated" padding="lg">
            <h4 className="text-lg font-semibold text-headline mb-4">
              {t('analysis.results.experience')}
            </h4>
            <div className="space-y-3">
              <div>
                <span className="text-sm text-caption">{t('analysis.results.years_experience')}</span>
                <p className="font-medium text-headline">{analysis.experience.years} years</p>
              </div>
              {analysis.experience.companies.length > 0 && (
                <div>
                  <span className="text-sm text-caption">{t('analysis.results.companies')}</span>
                  <p className="font-medium text-headline">
                    {analysis.experience.companies.slice(0, 3).join(', ')}
                    {analysis.experience.companies.length > 3 && '...'}
                  </p>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Recommendations */}
        {analysis.recommendations.length > 0 && (
          <Card variant="elevated" padding="lg">
            <h4 className="text-lg font-semibold text-headline mb-4">
              {t('analysis.results.recommendations')}
            </h4>
            <ul className="space-y-3">
              {analysis.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full bg-accent/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-accent">{index + 1}</span>
                  </div>
                  <span className="text-body-text">{recommendation}</span>
                </li>
              ))}
            </ul>
          </Card>
        )}

        {/* Red Flags */}
        {analysis.redFlags.length > 0 && (
          <Card variant="elevated" padding="lg" className="border-error/20">
            <h4 className="text-lg font-semibold text-headline mb-4 flex items-center">
              <IoWarning className="w-5 h-5 text-error mr-2" />
              {t('analysis.results.red_flags')}
            </h4>
            <ul className="space-y-2">
              {analysis.redFlags.map((flag, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 rounded-full bg-error mt-2 flex-shrink-0" />
                  <span className="text-body-text">{flag}</span>
                </li>
              ))}
            </ul>
          </Card>
        )}
      </div>
    </motion.div>
  );
};

export default AnalysisResults;
