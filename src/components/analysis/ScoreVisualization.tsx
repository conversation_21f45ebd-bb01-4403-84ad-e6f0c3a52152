import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  RadialBarChart,
  RadialBar
} from 'recharts';
import { useLanguage } from '@/hooks/useLanguage';
import Card from '@/components/common/Card';

interface ScoreVisualizationProps {
  scores: {
    technical: number;
    experience: number;
    communication: number;
    cultureFit: number;
  };
  overallScore: number;
}

const ScoreVisualization: React.FC<ScoreVisualizationProps> = ({ scores, overallScore }) => {
  const { t } = useLanguage();

  // Prepare data for charts
  const barData = [
    {
      name: t('analysis.categories.technical'),
      score: scores.technical,
      fill: '#3b82f6'
    },
    {
      name: t('analysis.categories.experience'),
      score: scores.experience,
      fill: '#10b981'
    },
    {
      name: t('analysis.categories.communication'),
      score: scores.communication,
      fill: '#f59e0b'
    },
    {
      name: t('analysis.categories.culture_fit'),
      score: scores.cultureFit,
      fill: '#8b5cf6'
    }
  ];

  const pieData = [
    {
      name: t('analysis.categories.technical'),
      value: scores.technical,
      fill: '#3b82f6'
    },
    {
      name: t('analysis.categories.experience'),
      value: scores.experience,
      fill: '#10b981'
    },
    {
      name: t('analysis.categories.communication'),
      value: scores.communication,
      fill: '#f59e0b'
    },
    {
      name: t('analysis.categories.culture_fit'),
      value: scores.cultureFit,
      fill: '#8b5cf6'
    }
  ];

  const radialData = [
    {
      name: 'Overall Score',
      value: overallScore,
      fill: overallScore >= 80 ? '#10b981' : overallScore >= 60 ? '#f59e0b' : '#ef4444'
    }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-headline font-medium">{label}</p>
          <p className="text-primary">
            Score: <span className="font-bold">{payload[0].value}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8">
      {/* Overall Score Radial Chart */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card variant="elevated" padding="lg">
          <h3 className="text-xl font-bold text-headline mb-6 text-center">
            {t('analysis.results.overall_score')}
          </h3>
          <div className="flex items-center justify-center">
            <div className="relative w-64 h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadialBarChart
                  cx="50%"
                  cy="50%"
                  innerRadius="60%"
                  outerRadius="90%"
                  data={radialData}
                  startAngle={90}
                  endAngle={-270}
                >
                  <RadialBar
                    dataKey="value"
                    cornerRadius={10}
                    fill={radialData[0].fill}
                  />
                </RadialBarChart>
              </ResponsiveContainer>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className={`text-4xl font-bold ${getScoreColor(overallScore)}`}>
                    {overallScore}%
                  </div>
                  <div className="text-sm text-caption mt-1">
                    Overall Score
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Score Breakdown Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Bar Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card variant="elevated" padding="lg">
            <h3 className="text-lg font-bold text-headline mb-6">
              {t('analysis.results.score_breakdown')} - Bar Chart
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={barData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  domain={[0, 100]}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="score" 
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </motion.div>

        {/* Pie Chart */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card variant="elevated" padding="lg">
            <h3 className="text-lg font-bold text-headline mb-6">
              {t('analysis.results.score_breakdown')} - Distribution
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${value}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  animationDuration={1000}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value) => <span className="text-sm">{value}</span>}
                />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </motion.div>
      </div>

      {/* Score Cards with Progress Bars */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card variant="elevated" padding="lg">
          <h3 className="text-lg font-bold text-headline mb-6">
            Detailed Score Breakdown
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {barData.map((item, index) => (
              <motion.div
                key={item.name}
                className="space-y-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-headline">{item.name}</span>
                  <span className={`text-sm font-bold ${getScoreColor(item.score)}`}>
                    {item.score}%
                  </span>
                </div>
                <div className="w-full bg-surface rounded-full h-3">
                  <motion.div
                    className="h-3 rounded-full"
                    style={{ backgroundColor: item.fill }}
                    initial={{ width: 0 }}
                    animate={{ width: `${item.score}%` }}
                    transition={{ duration: 1, delay: 0.6 + index * 0.1 }}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default ScoreVisualization;
