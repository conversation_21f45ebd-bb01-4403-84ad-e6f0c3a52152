import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/hooks/useTheme';
import { useLanguage } from '@/hooks/useLanguage';
import ThemeAwareIllustration from '../assets/ThemeAwareIllustration';
import Button from '../common/Button';
import Card from '../common/Card';

/**
 * Test component to validate theme-aware illustrations and Arabic font switching
 * This component can be temporarily added to any page for testing purposes
 */
const ThemeAndFontTest: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const { currentLanguage, changeLanguage, getSupportedLanguages } = useLanguage();
  const supportedLanguages = getSupportedLanguages();

  return (
    <Card variant="elevated" padding="xl" className="max-w-4xl mx-auto my-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-headline mb-2">
            Theme & Font Test Component
          </h2>
          <p className="text-body-text">
            Test theme-aware illustrations and Arabic font switching
          </p>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Theme Controls */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-headline">Theme Controls</h3>
            <div className="flex gap-2">
              <Button
                variant={theme === 'light' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setTheme('light')}
              >
                Light Theme
              </Button>
              <Button
                variant={theme === 'dark' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setTheme('dark')}
              >
                Dark Theme
              </Button>
            </div>
            <p className="text-sm text-caption">
              Current theme: <span className="font-medium">{theme}</span>
            </p>
          </div>

          {/* Language Controls */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-headline">Language Controls</h3>
            <div className="flex flex-wrap gap-2">
              {supportedLanguages.map((lang) => (
                <Button
                  key={lang.code}
                  variant={currentLanguage === lang.code ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => changeLanguage(lang.code)}
                >
                  {lang.nativeName}
                </Button>
              ))}
            </div>
            <p className="text-sm text-caption">
              Current language: <span className="font-medium">{currentLanguage}</span>
            </p>
          </div>
        </div>

        {/* Font Test */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-headline">Font Test</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-surface rounded-lg">
              <h4 className="font-medium text-headline mb-2">English Text</h4>
              <p className="text-body-text">
                The quick brown fox jumps over the lazy dog. This text should use Alexandria font.
              </p>
            </div>
            <div className="p-4 bg-surface rounded-lg">
              <h4 className="font-medium text-headline mb-2">Arabic Text</h4>
              <p className="text-body-text">
                مرحبا بكم في تطبيق فحص السيرة الذاتية. هذا النص يجب أن يستخدم خط عربي مناسب.
              </p>
            </div>
          </div>
        </div>

        {/* Illustration Test */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-headline">Theme-Aware Illustrations</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map((num) => (
              <motion.div
                key={num}
                className="p-4 bg-surface rounded-lg text-center"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <ThemeAwareIllustration
                  number={num as any}
                  className="w-full h-24 mb-2"
                />
                <p className="text-sm text-caption">Illustration {num}</p>
              </motion.div>
            ))}
          </div>
          <p className="text-sm text-caption text-center">
            Illustrations should automatically switch between light and dark versions based on the current theme.
          </p>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-primary/10 rounded-lg">
          <h4 className="font-medium text-headline mb-2">Testing Instructions</h4>
          <ul className="text-sm text-body-text space-y-1">
            <li>• Switch between light and dark themes to see illustrations change</li>
            <li>• Switch to Arabic language to see font change globally</li>
            <li>• Verify that Arabic text renders properly with appropriate fonts</li>
            <li>• Check that theme transitions are smooth and consistent</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default ThemeAndFontTest;
