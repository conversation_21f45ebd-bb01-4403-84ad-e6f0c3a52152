import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { LocalStorage, ResumeAnalysis } from '@/utils/storage';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import { IoPlay, IoTrash, IoEye } from 'react-icons/io5';

/**
 * Development helper component for testing analysis ID functionality
 * Only visible in development mode
 */
const AnalysisTestHelper: React.FC = () => {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);
  const [analyses, setAnalyses] = useState<ResumeAnalysis[]>([]);

  // Only render in development
  if (!import.meta.env.DEV) {
    return null;
  }

  const refreshAnalyses = () => {
    const allAnalyses = LocalStorage.getAllAnalyses();
    setAnalyses(allAnalyses);
  };

  const createTestAnalysis = () => {
    const testAnalysis: ResumeAnalysis = {
      id: `test-${Date.now()}`,
      fileName: 'test-resume.pdf',
      fileSize: 1024000,
      uploadDate: new Date().toISOString(),
      analysisDate: new Date().toISOString(),
      candidateName: '<PERSON> Doe (Test)',
      overallScore: 85,
      scores: {
        technical: 90,
        experience: 80,
        communication: 85,
        cultureFit: 85
      },
      summary: 'This is a test analysis for development purposes.',
      strengths: ['Strong technical skills', 'Good communication', 'Team player'],
      weaknesses: ['Limited leadership experience', 'Could improve project management skills'],
      recommendations: ['Consider for senior developer role', 'Provide leadership training'],
      skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
      experience: {
        years: 5,
        positions: ['Software Developer', 'Frontend Developer'],
        companies: ['Tech Corp', 'StartupXYZ']
      },
      education: [
        {
          degree: 'Bachelor of Computer Science',
          institution: 'University of Technology',
          year: '2019'
        }
      ],
      redFlags: ['Gap in employment history'],
      rawText: 'This is test resume text content...',
      multiLanguageData: {
        en: {
          summary: 'This is a test analysis for development purposes.',
          strengths: ['Strong technical skills', 'Good communication', 'Team player'],
          weaknesses: ['Limited leadership experience', 'Could improve project management skills'],
          recommendations: ['Consider for senior developer role', 'Provide leadership training'],
          skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
          redFlags: ['Gap in employment history'],
          candidateName: 'John Doe (Test)'
        },
        fr: {
          summary: 'Ceci est une analyse de test à des fins de développement.',
          strengths: ['Compétences techniques solides', 'Bonne communication', 'Esprit d\'équipe'],
          weaknesses: ['Expérience de leadership limitée', 'Pourrait améliorer les compétences en gestion de projet'],
          recommendations: ['Considérer pour un poste de développeur senior', 'Fournir une formation en leadership'],
          skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
          redFlags: ['Écart dans l\'historique d\'emploi'],
          candidateName: 'John Doe (Test)'
        },
        es: {
          summary: 'Este es un análisis de prueba para fines de desarrollo.',
          strengths: ['Habilidades técnicas sólidas', 'Buena comunicación', 'Jugador de equipo'],
          weaknesses: ['Experiencia de liderazgo limitada', 'Podría mejorar las habilidades de gestión de proyectos'],
          recommendations: ['Considerar para el puesto de desarrollador senior', 'Proporcionar entrenamiento de liderazgo'],
          skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
          redFlags: ['Brecha en el historial de empleo'],
          candidateName: 'John Doe (Test)'
        },
        ar: {
          summary: 'هذا تحليل تجريبي لأغراض التطوير.',
          strengths: ['مهارات تقنية قوية', 'تواصل جيد', 'روح الفريق'],
          weaknesses: ['خبرة قيادية محدودة', 'يمكن تحسين مهارات إدارة المشاريع'],
          recommendations: ['النظر في منصب مطور أول', 'توفير تدريب القيادة'],
          skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
          redFlags: ['فجوة في تاريخ التوظيف'],
          candidateName: 'John Doe (Test)'
        }
      },
      primaryLanguage: 'en'
    };

    LocalStorage.saveAnalysis(testAnalysis);
    refreshAnalyses();
  };

  const deleteAnalysis = (id: string) => {
    const allAnalyses = LocalStorage.getAllAnalyses();
    const filteredAnalyses = allAnalyses.filter(a => a.id !== id);
    LocalStorage.set('analyses', filteredAnalyses);
    refreshAnalyses();
  };

  const viewAnalysis = (id: string) => {
    navigate(`/analysis/${id}`);
  };

  React.useEffect(() => {
    if (isVisible) {
      refreshAnalyses();
    }
  }, [isVisible]);

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-20 left-4 z-50 bg-secondary text-white p-3 rounded-full shadow-lg hover:bg-secondary-hover transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Analysis Test Helper"
      >
        🧪
      </motion.button>

      {/* Test Panel */}
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, x: -300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -300 }}
          className="fixed left-4 bottom-36 z-40 w-96 max-h-[70vh] overflow-y-auto"
        >
          <Card variant="elevated" padding="lg" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-headline">Analysis Test Helper</h3>
              <button
                onClick={() => setIsVisible(false)}
                className="text-caption hover:text-headline transition-colors"
              >
                ✕
              </button>
            </div>

            {/* Create Test Analysis */}
            <div className="space-y-2">
              <h4 className="font-semibold text-headline">Create Test Data</h4>
              <Button
                variant="primary"
                size="sm"
                onClick={createTestAnalysis}
                leftIcon={<IoPlay />}
                className="w-full"
              >
                Create Test Analysis
              </Button>
            </div>

            {/* Existing Analyses */}
            <div className="space-y-2">
              <h4 className="font-semibold text-headline">Existing Analyses ({analyses.length})</h4>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {analyses.map((analysis) => (
                  <div key={analysis.id} className="p-3 bg-surface rounded border">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-headline truncate">
                        {analysis.candidateName || 'Anonymous'}
                      </span>
                      <span className="text-xs text-caption">
                        {analysis.overallScore}%
                      </span>
                    </div>
                    <div className="text-xs text-caption mb-2">
                      ID: {analysis.id}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="xs"
                        onClick={() => viewAnalysis(analysis.id)}
                        leftIcon={<IoEye />}
                      >
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="xs"
                        onClick={() => deleteAnalysis(analysis.id)}
                        leftIcon={<IoTrash />}
                        className="text-error hover:text-error"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                ))}
                {analyses.length === 0 && (
                  <p className="text-sm text-caption text-center py-4">
                    No analyses found. Create a test analysis to get started.
                  </p>
                )}
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </>
  );
};

export default AnalysisTestHelper;
