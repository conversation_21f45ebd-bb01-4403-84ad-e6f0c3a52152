import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import { useFontLoadingStatus } from '@/hooks/useFontManager';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import { LANGUAGE_FONTS } from '@/utils/fontManager';

/**
 * Development component for testing font functionality
 * Only visible in development mode
 */
const FontTestComponent: React.FC = () => {
  const { currentLanguage, changeLanguage, getSupportedLanguages, t } = useLanguage();
  const { loadedFonts, isLanguageFontLoaded } = useFontLoadingStatus();
  const [isVisible, setIsVisible] = useState(false);

  // Only render in development
  if (!import.meta.env.DEV) {
    return null;
  }

  const supportedLanguages = getSupportedLanguages();

  const testTexts = {
    en: 'The quick brown fox jumps over the lazy dog. ABCDEFGHIJKLMNOPQRSTUVWXYZ 1234567890',
    fr: 'Portez ce vieux whisky au juge blond qui fume. ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÑÒÓÔÕÖØÙÚÛÜÝ',
    es: 'El veloz murciélago hindú comía feliz cardillo y kiwi. ÁÉÍÓÚÜÑ¿¡ 1234567890',
    ar: 'نص حكيم له سر قاطع وذو شأن عظيم مكتوب على ثوب أخضر ومغلف بجلد أزرق. ١٢٣٤٥٦٧٨٩٠'
  };

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 z-50 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-hover transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Font Test Panel"
      >
        🎨
      </motion.button>

      {/* Test Panel */}
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, x: -300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -300 }}
          className="fixed left-4 bottom-20 z-40 w-96 max-h-[80vh] overflow-y-auto"
        >
          <Card variant="elevated" padding="lg" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-headline">Font Test Panel</h3>
              <button
                onClick={() => setIsVisible(false)}
                className="text-caption hover:text-headline transition-colors"
              >
                ✕
              </button>
            </div>

            {/* Current Language Info */}
            <div className="space-y-2">
              <h4 className="font-semibold text-headline">Current Language</h4>
              <div className="text-sm space-y-1">
                <p><strong>Code:</strong> {currentLanguage}</p>
                <p><strong>Font:</strong> {LANGUAGE_FONTS[currentLanguage]?.name || 'Unknown'}</p>
                <p><strong>Loaded:</strong> {isLanguageFontLoaded(currentLanguage) ? '✅' : '❌'}</p>
              </div>
            </div>

            {/* Language Switcher */}
            <div className="space-y-3">
              <h4 className="font-semibold text-headline">Switch Language</h4>
              <div className="grid grid-cols-2 gap-2">
                {supportedLanguages.map((lang) => (
                  <Button
                    key={lang.code}
                    variant={currentLanguage === lang.code ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => changeLanguage(lang.code)}
                    className="text-xs"
                  >
                    {lang.nativeName}
                    {isLanguageFontLoaded(lang.code) && ' ✅'}
                  </Button>
                ))}
              </div>
            </div>

            {/* Font Loading Status */}
            <div className="space-y-2">
              <h4 className="font-semibold text-headline">Font Status</h4>
              <div className="text-xs space-y-1">
                {Object.entries(LANGUAGE_FONTS).map(([code, config]) => (
                  <div key={code} className="flex justify-between">
                    <span>{code.toUpperCase()}: {config.name}</span>
                    <span>{loadedFonts.includes(code) ? '✅' : '⏳'}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Font Preview */}
            <div className="space-y-4">
              <h4 className="font-semibold text-headline">Font Preview</h4>
              {Object.entries(testTexts).map(([langCode, text]) => (
                <div key={langCode} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-headline">
                      {langCode.toUpperCase()} - {LANGUAGE_FONTS[langCode]?.name}
                    </span>
                    <span className="text-xs text-caption">
                      {loadedFonts.includes(langCode) ? '✅' : '⏳'}
                    </span>
                  </div>
                  <div 
                    className="p-3 bg-surface rounded border text-sm leading-relaxed"
                    style={{ 
                      fontFamily: LANGUAGE_FONTS[langCode]?.family,
                      direction: LANGUAGE_FONTS[langCode]?.rtl ? 'rtl' : 'ltr'
                    }}
                  >
                    {text}
                  </div>
                </div>
              ))}
            </div>

            {/* Font Weights Test */}
            <div className="space-y-3">
              <h4 className="font-semibold text-headline">Font Weights</h4>
              <div className="space-y-2">
                {[400, 500, 600, 700].map((weight) => (
                  <div key={weight} className="flex items-center space-x-3">
                    <span className="text-xs text-caption w-8">{weight}</span>
                    <span 
                      className="flex-1 text-sm"
                      style={{ fontWeight: weight }}
                    >
                      {testTexts[currentLanguage as keyof typeof testTexts]?.substring(0, 30)}...
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </>
  );
};

export default FontTestComponent;
