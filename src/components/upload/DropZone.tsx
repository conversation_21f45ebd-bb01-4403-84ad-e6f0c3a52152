import AnalysisLoading from '@/components/analysis/AnalysisLoading';
import AnalysisResults from '@/components/analysis/AnalysisResults';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import SuccessConfetti from '@/components/common/SuccessConfetti';
import { useLanguage } from '@/hooks/useLanguage';
import { GeminiAPI, GeminiAnalysisResponse } from '@/utils/gemini';
import { PDFParser } from '@/utils/pdf';
import { ReportGenerator } from '@/utils/reportGenerator';
import { CookieStorage, LocalStorage, ResumeAnalysis } from '@/utils/storage';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { IoCheckmark, IoClose, IoCloudUpload, IoDocument } from 'react-icons/io5';

interface DropZoneProps {
  onFileProcessed: (file: File, extractedText: string) => void;
  onError: (error: string) => void;
  disabled?: boolean;
}

interface FileState {
  file: File | null;
  status: 'idle' | 'processing' | 'analyzing' | 'success' | 'error';
  extractedText?: string;
  analysis?: GeminiAnalysisResponse;
  error?: string;
  fileInfo?: {
    name: string;
    size: string;
    type: string;
    pages?: number;
  };
}

const DropZone: React.FC<DropZoneProps> = ({ onFileProcessed, onError, disabled = false }) => {
  const { t, currentLanguage } = useLanguage();
  const [fileState, setFileState] = useState<FileState>({
    file: null,
    status: 'idle'
  });
  const [showConfetti, setShowConfetti] = useState(false);


  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    setFileState({ file, status: 'processing' });

    try {
      // Validate PDF
      const validation = await PDFParser.validatePDF(file);
      if (!validation.isValid) {
        throw new Error(validation.error || t('upload.errors.invalid_format'));
      }

      // Extract text from PDF
      const pdfInfo = await PDFParser.extractTextFromFile(file);
      const fileInfo = await PDFParser.getFileInfo(file);

      if (!pdfInfo.text || pdfInfo.text.trim().length < 50) {
        throw new Error(t('upload.errors.parse_failed'));
      }

      // Update state with extracted text
      setFileState({
        file,
        status: 'analyzing',
        extractedText: pdfInfo.text,
        fileInfo: {
          ...fileInfo,
          pages: pdfInfo.numPages
        }
      });

      onFileProcessed(file, pdfInfo.text);

      // Check for cookie consent before AI analysis
      const hasConsent = CookieStorage.getCookieConsent();
      if (hasConsent !== true) {
        throw new Error(t('upload.errors.cookie_consent_required'));
      }

      // Start AI analysis
      const apiKey = CookieStorage.getApiKey();
      if (!apiKey) {
        throw new Error(t('upload.errors.no_api_key'));
      }

      // Generate multi-language analysis
      const multiLanguageAnalysis = await GeminiAPI.analyzeResumeMultiLanguage(
        {
          resumeText: pdfInfo.text,
          language: currentLanguage
        },
        apiKey,
        ['en', 'fr', 'es', 'ar'] // Generate for all supported languages
      );

      const analysis = multiLanguageAnalysis.baseAnalysis;

      // Prepare multi-language data
      const multiLanguageData: { [languageCode: string]: any } = {};
      Object.entries(multiLanguageAnalysis.translations).forEach(([lang, translatedAnalysis]) => {
        multiLanguageData[lang] = {
          summary: translatedAnalysis.summary,
          strengths: translatedAnalysis.strengths,
          weaknesses: translatedAnalysis.weaknesses,
          recommendations: translatedAnalysis.recommendations,
          skills: translatedAnalysis.skills,
          redFlags: translatedAnalysis.redFlags,
          candidateName: translatedAnalysis.candidateName
        };
      });

      // Save analysis to storage
      const analysisRecord: ResumeAnalysis = {
        id: multiLanguageAnalysis.analysisId,
        fileName: file.name,
        fileSize: file.size,
        uploadDate: new Date().toISOString(),
        analysisDate: multiLanguageAnalysis.createdAt,
        candidateName: analysis.candidateName,
        overallScore: analysis.overallScore,
        scores: analysis.scores,
        summary: analysis.summary,
        strengths: analysis.strengths,
        weaknesses: analysis.weaknesses,
        recommendations: analysis.recommendations,
        skills: analysis.skills,
        experience: analysis.experience,
        education: analysis.education,
        redFlags: analysis.redFlags || [],
        rawText: pdfInfo.text,
        geminiResponse: analysis,
        multiLanguageData,
        primaryLanguage: currentLanguage
      };

      LocalStorage.saveAnalysis(analysisRecord);

      // Update state with analysis results
      setFileState({
        file,
        status: 'success',
        extractedText: pdfInfo.text,
        analysis,
        fileInfo: {
          ...fileInfo,
          pages: pdfInfo.numPages
        }
      });

      // Trigger success confetti
      setShowConfetti(true);

      // Results will be shown inline automatically

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('upload.errors.parse_failed');
      setFileState({
        file,
        status: 'error',
        error: errorMessage
      });
      onError(errorMessage);
    }
  }, [onFileProcessed, onError, t]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB
    disabled: disabled || fileState.status === 'processing' || fileState.status === 'analyzing'
  });

  const removeFile = () => {
    setFileState({ file: null, status: 'idle' });
  };

  const getDropzoneStatus = () => {
    if (isDragReject) return 'reject';
    if (isDragActive) return 'active';
    if (fileState.status === 'processing' || fileState.status === 'analyzing') return 'processing';
    if (fileState.file) return 'success';
    return 'idle';
  };

  const status = getDropzoneStatus();

  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-xl p-8 smooth-hover transition-all duration-base cursor-pointer
          ${status === 'active' ? 'border-primary bg-primary/5 scale-105' : ''}
          ${status === 'reject' ? 'border-error bg-error/5' : ''}
          ${status === 'idle' ? 'border-border hover:border-primary hover:bg-surface' : ''}
          ${status === 'processing' ? 'border-warning bg-warning/5' : ''}
          ${status === 'success' ? 'border-success bg-success/5' : ''}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />

        <AnimatePresence mode="wait">
          <motion.div
            key={status}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="text-center"
          >
            {/* Icon */}
            <div className="flex justify-center mb-4">
              {status === 'processing' && (
                <div className="w-12 h-12 rounded-full bg-warning/20 flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-warning border-t-transparent rounded-full animate-spin-fast" />
                </div>
              )}

              {status === 'success' && (
                <motion.div
                  className="w-12 h-12 rounded-full bg-success/20 flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                >
                  <IoCheckmark className="w-6 h-6 text-success" />
                </motion.div>
              )}

              {(status === 'idle' || status === 'active') && (
                <motion.div
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${status === 'active'
                    ? 'bg-primary/20 text-primary'
                    : 'bg-surface-variant text-caption'
                    }`}
                  animate={status === 'active' ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 0.6, repeat: Infinity }}
                >
                  <IoCloudUpload className="w-6 h-6" />
                </motion.div>
              )}

              {status === 'reject' && (
                <div className="w-12 h-12 rounded-full bg-error/20 flex items-center justify-center">
                  <IoClose className="w-6 h-6 text-error" />
                </div>
              )}
            </div>

            {/* Text */}
            <div className="space-y-2">
              <p className="text-lg font-medium text-headline">
                {status === 'active' && t('upload.dropzone.active')}
                {status === 'idle' && t('upload.dropzone.inactive')}
                {status === 'processing' && t('upload.status.parsing')}
                {status === 'success' && t('upload.status.complete')}
                {status === 'reject' && t('upload.dropzone.error')}
              </p>

              <p className="text-sm text-caption">
                {status !== 'success' && t('upload.dropzone.accepted')}
                {status === 'success' && t('upload.description')}
              </p>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* File Info */}
      <AnimatePresence>
        {fileState.file && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card variant="elevated" padding="md">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <IoDocument className="w-5 h-5 text-primary" />
                  </div>

                  <div className="flex-1">
                    <h3 className="font-medium text-headline">{fileState.file.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-caption">
                      <span>{PDFParser.formatFileSize(fileState.file.size)}</span>
                      {fileState.fileInfo?.pages && (
                        <span>{fileState.fileInfo.pages} {t('upload.file_info.pages')}</span>
                      )}
                      <span className={`px-2 py-1 rounded-full text-xs ${fileState.status === 'success'
                        ? 'bg-success/10 text-success'
                        : fileState.status === 'error'
                          ? 'bg-error/10 text-error'
                          : 'bg-warning/10 text-warning'
                        }`}>
                        {t(`upload.status.${fileState.status}`)}
                      </span>
                    </div>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={removeFile}
                  className="p-2"
                >
                  <IoClose className="w-4 h-4" />
                </Button>
              </div>

              {fileState.error && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mt-3 p-3 bg-error/10 border border-error/20 rounded-lg"
                >
                  <p className="text-sm text-error">{fileState.error}</p>
                </motion.div>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Analysis Loading Modal */}
      <AnimatePresence>
        {fileState.status === 'analyzing' && fileState.file && (
          <AnalysisLoading
            fileName={fileState.file.name}
            onCancel={() => {
              setFileState({ file: null, status: 'idle' });
            }}
          />
        )}
      </AnimatePresence>

      {/* Analysis Results Inline */}
      <AnimatePresence>
        {fileState.status === 'success' && fileState.analysis && fileState.file && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-headline">
                {t('analysis.results.title')}
              </h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFileState({ file: null, status: 'idle' })}
                leftIcon={<IoCloudUpload />}
              >
                {t('analysis.actions.analyze_another')}
              </Button>
            </div>

            <AnalysisResults
              analysis={fileState.analysis}
              fileName={fileState.file.name}
              onSave={() => setFileState({ file: null, status: 'idle' })}
              onDownload={() => {
                if (fileState.analysis && fileState.file) {
                  // Convert GeminiAnalysisResponse to ResumeAnalysis for download
                  const analysisRecord: ResumeAnalysis = {
                    id: Date.now().toString(),
                    fileName: fileState.file.name,
                    fileSize: fileState.file.size,
                    uploadDate: new Date().toISOString(),
                    analysisDate: new Date().toISOString(),
                    candidateName: fileState.analysis.candidateName,
                    overallScore: fileState.analysis.overallScore,
                    scores: fileState.analysis.scores,
                    summary: fileState.analysis.summary,
                    strengths: fileState.analysis.strengths,
                    weaknesses: fileState.analysis.weaknesses,
                    recommendations: fileState.analysis.recommendations,
                    skills: fileState.analysis.skills,
                    experience: fileState.analysis.experience,
                    education: fileState.analysis.education,
                    redFlags: fileState.analysis.redFlags || [],
                    rawText: fileState.extractedText || '',
                    geminiResponse: fileState.analysis
                  };
                  ReportGenerator.downloadHTMLReport(analysisRecord);
                }
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Confetti */}
      <SuccessConfetti
        show={showConfetti}
        onComplete={() => setShowConfetti(false)}
        duration={3000}
        numberOfPieces={150}
      />
    </div>
  );
};

export default DropZone;
