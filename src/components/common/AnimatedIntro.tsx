import React, { useEffect, useRef, useState } from "react";
import gsap from "gsap";

// Utility: prevent body scroll while intro visible
const useLockBodyScroll = (locked: boolean) => {
  useEffect(() => {
    if (locked) {
      const original = document.body.style.overflow;
      document.body.style.overflow = "hidden";
      return () => {
        document.body.style.overflow = original;
      };
    }
  }, [locked]);
};

const AnimatedIntro: React.FC<{ onFinish?: () => void }> = ({ onFinish }) => {
  const bgRef = useRef<HTMLDivElement | null>(null);
  const blobRef = useRef<SVGPathElement | null>(null);
  const logoRef = useRef<SVGTextElement | null>(null);
  const taglineRef = useRef<HTMLDivElement | null>(null);
  const [done, setDone] = useState(false);

  useLockBodyScroll(!done);

  useEffect(() => {
    if (done) return;
    const tl = gsap.timeline({
      defaults: { ease: "power2.out" },
      onComplete: () => {
        // Fade out overlay after intro
        gsap.to(bgRef.current, {
          opacity: 0,
          duration: 0.6,
          pointerEvents: "none",
          onComplete: () => {
            setDone(true);
            onFinish?.();
          },
        });
      },
    });

    tl.fromTo(
      bgRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.7 }
    )
      .fromTo(
        blobRef.current,
        { opacity: 0, scale: 0.8, rotate: -10, transformOrigin: "50% 50%" },
        { opacity: 0.13, scale: 1, rotate: 3, duration: 1.2, ease: "expo.out" },
        "-=0.2"
      )
      .fromTo(
        logoRef.current,
        { opacity: 0, y: 40, scale: 0.9, letterSpacing: "0.25em" },
        { opacity: 1, y: 0, scale: 1, letterSpacing: "normal", duration: 0.9, ease: "expo.out" },
        "-=0.8"
      )
      .fromTo(
        taglineRef.current,
        { opacity: 0, y: 24 },
        { opacity: 1, y: 0, duration: 0.65 },
        "-=0.3"
      );
    // Optionally, auto-dismiss a few seconds after all anim is done (user can still skip earlier)
    tl.to({}, { duration: 1.5 }); // extra pause
  }, [done, onFinish]);

  if (done) return null;

  // Responsive logic for downsizing blob and logo if small screen
  const isMobile = window.innerWidth < 600;
  const blobW = isMobile ? 340 : 560;
  const blobH = isMobile ? 200 : 410;

  return (
    <div
      ref={bgRef}
      style={{
        position: "fixed",
        zIndex: 9999,
        inset: 0,
        width: "100vw",
        height: "100vh",
        background: "linear-gradient(135deg, hsl(var(--brand-primary)), hsl(var(--brand-secondary)))",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "column",
        transition: "background var(--transition-base)",
        overflow: "hidden",
      }}
      aria-modal="true"
      role="dialog"
    >
      {/* Decorative animated blob */}
      <svg
        width={blobW}
        height={blobH}
        viewBox="0 0 560 410"
        style={{
          position: "absolute",
          left: "50%",
          top: isMobile ? "38%" : "48%",
          transform: "translate(-50%,-50%)",
          zIndex: 0,
          pointerEvents: "none",
        }}
      >
        <defs>
          <radialGradient id="ai-blob" cx="30%" cy="27%" r="1">
            <stop offset="0%" stopColor="hsl(var(--accent) / 0.32)" />
            <stop offset="100%" stopColor="hsl(var(--brand-secondary) / 0)" />
          </radialGradient>
        </defs>
        <path
          ref={blobRef}
          d="
            M229,62Q250,25,318,47Q386,69,408,132Q430,194,426,265Q422,336,351,350Q280,364,222,353Q164,342,153,274Q142,206,167,146Q192,86,229,62Z
          "
          fill="url(#ai-blob)"
        />
      </svg>

      <div style={{
        position: "relative",
        zIndex: 2,
        textAlign: "center",
        color: "hsl(var(--headline))",
        marginTop: isMobile ? 20 : 0,
        width: isMobile ? "92vw" : 420
      }}>
        {/* Logo with subtle wiggle animation */}
        <svg width={isMobile ? 100 : 160} height={isMobile ? 36 : 60} viewBox="0 0 180 68" style={{margin: "0 auto"}}>
          <text
            ref={logoRef}
            x="50%"
            y="56%"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={isMobile ? 32 : 48}
            fontWeight="bold"
            letterSpacing="0.08em"
            fill="hsl(var(--brand-primary))"
            style={{
              filter: "drop-shadow(0 2px 10px hsl(var(--brand-primary) / 0.26))"
            }}
          >
            4cv
          </text>
        </svg>
        <div
          ref={taglineRef}
          style={{
            marginTop: 24,
            fontSize: isMobile ? 17 : 22,
            color: "hsl(var(--headline))",
            fontWeight: 600,
            letterSpacing: "-0.01em",
            opacity: 0,
            transition: "opacity 0.6s"
          }}
        >
          Transform Your Hiring Process with <span style={{color:"hsl(var(--primary))"}}>AI</span>
          <div style={{
            marginTop: 14,
            fontSize: isMobile ? 14 : 16,
            color: "hsl(var(--body-text) / 0.93)",
            fontWeight: 400,
            lineHeight: 1.4,
            maxWidth: isMobile ? "86vw" : 420,
            marginLeft: "auto",
            marginRight: "auto",
          }}>
            Get instant, comprehensive candidate insights with our advanced <span style={{color:"hsl(var(--accent))"}}>AI-powered resume analysis</span> platform.
          </div>
        </div>
      </div>

      {/* Skip Intro button for accessibility & testing */}
      <button
        onClick={() => setDone(true)}
        style={{
          position: "absolute",
          bottom: 34,
          right: 40,
          zIndex: 4,
          color: "hsl(var(--headline))",
          background: "hsl(var(--background) / 0.85)",
          border: "none",
          padding: "12px 26px",
          borderRadius: "var(--radius)",
          fontWeight: 600,
          fontSize: 16,
          cursor: "pointer",
          boxShadow: "var(--shadow-sm)",
          transition: "opacity 0.18s",
          opacity: 0.85
        }}
        aria-label="Skip intro animation"
      >
        Skip
      </button>
    </div>
  );
};

export default AnimatedIntro;
