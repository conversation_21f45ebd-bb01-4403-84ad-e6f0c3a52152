import { useLanguage } from "@/hooks/useLanguage";
import { usePageTitle } from "@/hooks/usePageTitle";
import { motion, Variants } from "framer-motion";
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  IoHome, 
  IoArrowBack, 
  IoShieldOutline, 
  IoServerOutline, 
  IoConstructOutline,
  IoSearchOutline,
  IoWarningOutline
} from "react-icons/io5";

export interface ErrorPageProps {
  statusCode: number;
  title?: string;
  message?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  customIcon?: React.ReactNode;
  onCustomAction?: () => void;
  customActionLabel?: string;
}

const fadeVariants: Variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.65, ease: "easeOut" } },
};

const getErrorIcon = (statusCode: number): React.ReactNode => {
  switch (statusCode) {
    case 403:
      return <IoShieldOutline className="w-16 h-16 text-primary/60" />;
    case 404:
      return <IoSearchOutline className="w-16 h-16 text-primary/60" />;
    case 500:
      return <IoServerOutline className="w-16 h-16 text-primary/60" />;
    case 503:
      return <IoConstructOutline className="w-16 h-16 text-primary/60" />;
    default:
      return <IoWarningOutline className="w-16 h-16 text-primary/60" />;
  }
};

const getDefaultTitle = (statusCode: number): string => {
  switch (statusCode) {
    case 403:
      return "Access Denied";
    case 404:
      return "Page Not Found";
    case 500:
      return "Server Error";
    case 503:
      return "Service Unavailable";
    default:
      return "Error";
  }
};

const getDefaultMessage = (statusCode: number): string => {
  switch (statusCode) {
    case 403:
      return "You don't have permission to access this resource.";
    case 404:
      return "The page you're looking for doesn't exist.";
    case 500:
      return "Something went wrong on our end. Please try again later.";
    case 503:
      return "The service is temporarily unavailable. Please try again later.";
    default:
      return "An unexpected error occurred.";
  }
};

const ErrorPage: React.FC<ErrorPageProps> = ({
  statusCode,
  title,
  message,
  showBackButton = true,
  showHomeButton = true,
  customIcon,
  onCustomAction,
  customActionLabel
}) => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const errorTitle = title || getDefaultTitle(statusCode);
  const errorMessage = message || getDefaultMessage(statusCode);
  const errorIcon = customIcon || getErrorIcon(statusCode);

  usePageTitle(`${statusCode} - ${errorTitle}`);

  useEffect(() => {
    console.error(`${statusCode} Error: ${errorTitle} - ${errorMessage}`);
  }, [statusCode, errorTitle, errorMessage]);

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center bg-background px-4"
      initial="hidden"
      animate="visible"
      variants={fadeVariants}
    >
      <main
        className="text-center max-w-lg mx-auto px-8 py-12 rounded-2xl shadow-lg bg-surface/80 border border-border"
        aria-labelledby="error-heading"
        role="main"
      >
        {/* Error Illustration */}
        <div className="mb-8">
          <div className="relative mx-auto w-48 h-48 flex items-center justify-center">
            {/* Large Status Code */}
            <div className="text-8xl font-bold text-primary/20 select-none">
              {statusCode}
            </div>
            {/* Icon Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              {errorIcon}
            </div>
          </div>
        </div>

        <h1 id="error-heading" className="text-4xl font-bold mb-4 text-headline">
          {errorTitle}
        </h1>
        <p className="text-xl text-body-text mb-8 leading-relaxed">
          {errorMessage}
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showHomeButton && (
            <button
              onClick={() => navigate('/')}
              className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-primary rounded-lg shadow hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition"
            >
              <IoHome className="w-5 h-5 mr-2" />
              {t("pages.not_found.return_home")}
            </button>
          )}
          
          {showBackButton && (
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-primary bg-transparent border border-primary rounded-lg hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition"
            >
              <IoArrowBack className="w-5 h-5 mr-2" />
              {t("pages.not_found.go_back")}
            </button>
          )}

          {onCustomAction && customActionLabel && (
            <button
              onClick={onCustomAction}
              className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-primary bg-transparent border border-primary rounded-lg hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition"
            >
              {customActionLabel}
            </button>
          )}
        </div>
      </main>
    </motion.div>
  );
};

export default ErrorPage;
