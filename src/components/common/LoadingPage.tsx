import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';

interface LoadingPageProps {
  title?: string;
  message?: string;
  showProgress?: boolean;
  progress?: number;
  variant?: 'fullscreen' | 'inline' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
}

const LoadingPage: React.FC<LoadingPageProps> = ({
  title,
  message,
  showProgress = false,
  progress = 0,
  variant = 'fullscreen',
  size = 'md'
}) => {
  const { t } = useLanguage();

  const defaultTitle = title || t('common.loading');
  const defaultMessage = message || '';

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'w-full max-w-sm',
      logo: 'w-16 h-16',
      logoText: 'text-2xl',
      title: 'text-lg',
      message: 'text-sm',
      spinner: 'w-8 h-8',
      padding: 'p-6'
    },
    md: {
      container: 'w-full max-w-md',
      logo: 'w-20 h-20',
      logoText: 'text-3xl',
      title: 'text-xl',
      message: 'text-base',
      spinner: 'w-10 h-10',
      padding: 'p-8'
    },
    lg: {
      container: 'w-full max-w-lg',
      logo: 'w-24 h-24',
      logoText: 'text-4xl',
      title: 'text-2xl',
      message: 'text-lg',
      spinner: 'w-12 h-12',
      padding: 'p-10'
    }
  };

  const config = sizeConfig[size];

  // Logo component with animation
  const AnimatedLogo = () => (
    <motion.div
      className={`${config.logo} mx-auto mb-6 rounded-full bg-gradient-primary flex items-center justify-center relative overflow-hidden`}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Rotating background gradient */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-secondary/20"
        animate={{ rotate: 360 }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
      />
      
      {/* 4CV Logo */}
      <motion.div
        className={`${config.logoText} font-bold text-white relative z-10`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        4CV
      </motion.div>
      
      {/* Pulse effect */}
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-white/30"
        animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      />
    </motion.div>
  );

  // Spinner component
  const Spinner = () => (
    <motion.div
      className={`${config.spinner} border-3 border-primary/20 border-t-primary rounded-full mx-auto`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  );

  // Progress bar component
  const ProgressBar = () => (
    showProgress && (
      <motion.div
        className="w-full mt-6"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <div className="w-full bg-surface/50 rounded-full h-2 mb-2">
          <motion.div
            className="h-2 bg-gradient-primary rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          />
        </div>
        <p className="text-sm text-caption text-center">
          {Math.round(Math.min(100, Math.max(0, progress)))}%
        </p>
      </motion.div>
    )
  );

  // Content component
  const LoadingContent = () => (
    <motion.div
      className={`${config.container} ${config.padding} bg-surface/80 backdrop-blur-sm rounded-2xl border border-border shadow-custom-lg text-center`}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
    >
      {variant !== 'minimal' && <AnimatedLogo />}
      
      {variant === 'minimal' && (
        <div className="mb-6">
          <Spinner />
        </div>
      )}

      <motion.h2
        className={`${config.title} font-bold text-headline mb-3`}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {defaultTitle}
      </motion.h2>

      {defaultMessage && (
        <motion.p
          className={`${config.message} text-body-text mb-4`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          {defaultMessage}
        </motion.p>
      )}

      <ProgressBar />

      {/* Floating dots animation */}
      <motion.div
        className="flex justify-center space-x-1 mt-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-primary rounded-full"
            animate={{ y: [0, -8, 0] }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              delay: index * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </motion.div>
    </motion.div>
  );

  // Render based on variant
  if (variant === 'inline') {
    return (
      <div className="w-full flex justify-center py-8">
        <LoadingContent />
      </div>
    );
  }

  if (variant === 'fullscreen') {
    return (
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-background/80 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LoadingContent />
      </motion.div>
    );
  }

  // minimal variant
  return <LoadingContent />;
};

export default LoadingPage;
