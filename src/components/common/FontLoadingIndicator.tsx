import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import { useFontLoadingStatus } from '@/hooks/useFontManager';

interface FontLoadingIndicatorProps {
  show?: boolean;
  className?: string;
}

/**
 * Component to show font loading progress
 * Useful for development and debugging
 */
const FontLoadingIndicator: React.FC<FontLoadingIndicatorProps> = ({ 
  show = false, 
  className = '' 
}) => {
  const { t } = useLanguage();
  const { loadedFonts, totalFonts, totalFontsLoaded } = useFontLoadingStatus();

  if (!show || totalFontsLoaded === totalFonts) {
    return null;
  }

  const progress = (totalFontsLoaded / totalFonts) * 100;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`fixed top-4 right-4 z-50 bg-background border border-border rounded-lg p-4 shadow-lg max-w-sm ${className}`}
    >
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-headline">
            Loading Fonts
          </h4>
          <span className="text-xs text-caption">
            {totalFontsLoaded}/{totalFonts}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-surface rounded-full h-2">
          <motion.div
            className="bg-primary h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>

        {/* Loaded Fonts List */}
        <div className="space-y-1">
          {loadedFonts.map((lang) => (
            <div key={lang} className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success rounded-full" />
              <span className="text-xs text-caption">
                {lang.toUpperCase()} font loaded
              </span>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Development-only font loading indicator
 */
export const DevFontLoadingIndicator: React.FC = () => {
  // Only show in development mode
  const isDev = import.meta.env.DEV;
  
  return <FontLoadingIndicator show={isDev} />;
};

export default FontLoadingIndicator;
