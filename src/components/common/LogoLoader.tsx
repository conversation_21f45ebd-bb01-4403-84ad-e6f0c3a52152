import { motion } from "framer-motion";

// Replace the `d` attribute values below with precise paths for your custom "4cv" logo from your designer/SVG editor
const paths = [
  // Number 4
  "M10 80 L30 20 M30 20 L30 80 M30 80 L50 80",
  // Letter "c"
  "M70 60 Q70 80 90 80 Q110 80 110 60 Q110 40 90 40 Q70 40 70 60",
  // Letter "v"
  "M120 40 L135 80 L150 40",
];

const AnimationDuration = 2; // seconds per path

const LogoLoader = () => (
  <div className="z-50 fixed inset-0 bg-black/70 flex items-center justify-center">
    <motion.svg
      width="180"
      height="100"
      viewBox="0 0 170 100"
      fill="none"
      stroke="url(#logo-gradient)"
      strokeWidth="6"
    >
      <defs>
        <linearGradient id="logo-gradient" x1="0" y1="0" x2="1" y2="0">
          <stop offset="0%" stopColor="#3A36DB"/>
          <stop offset="100%" stopColor="#33D2FF"/>
        </linearGradient>
      </defs>
      {paths.map((d, i) => (
        <motion.path
          key={i}
          d={d}
          strokeLinecap="round"
          initial={{ pathLength: 0, opacity: 0.6 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{
            delay: i * 0.35, // stagger the lines
            duration: AnimationDuration,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
      ))}
    </motion.svg>
  </div>
);

export default LogoLoader;
