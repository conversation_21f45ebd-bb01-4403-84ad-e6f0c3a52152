import React, { useEffect, useState } from 'react';
import Confetti from 'react-confetti';
import { useWindowSize } from '@uidotdev/usehooks';

interface SuccessConfettiProps {
  /** Whether to show confetti */
  show: boolean;
  /** Duration in milliseconds */
  duration?: number;
  /** Number of confetti pieces */
  numberOfPieces?: number;
  /** Callback when confetti ends */
  onComplete?: () => void;
}

/**
 * Success confetti component for celebrating user achievements
 * Use this when:
 * - Resume upload is successful
 * - Analysis completes successfully
 * - User completes onboarding
 */
const SuccessConfetti: React.FC<SuccessConfettiProps> = ({
  show,
  duration = 3000,
  numberOfPieces = 200,
  onComplete
}) => {
  const { width, height } = useWindowSize();
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (show) {
      setIsActive(true);
      
      const timer = setTimeout(() => {
        setIsActive(false);
        onComplete?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [show, duration, onComplete]);

  if (!isActive || !width || !height) {
    return null;
  }

  return (
    <Confetti
      width={width}
      height={height}
      numberOfPieces={numberOfPieces}
      recycle={false}
      gravity={0.3}
      colors={[
        '#6366f1', // Primary brand color
        '#8b5cf6', // Secondary brand color
        '#06b6d4', // Tertiary brand color
        '#10b981', // Success green
        '#f59e0b', // Warning yellow
        '#ef4444', // Error red (for variety)
      ]}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: 9999,
        pointerEvents: 'none',
      }}
    />
  );
};

export default SuccessConfetti;
