import { useTheme } from '@/hooks/useTheme';
import { Expand } from "@theme-toggles/react";
import "@theme-toggles/react/css/Expand.css";
import React from 'react';

const ThemeToggle: React.FC = () => {
  const { theme, setTheme, effectiveTheme } = useTheme();


  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <button
      onClick={toggleTheme}
      className="relative p-2 rounded-lg hover:bg-surface/40 transition-colors duration-200"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
    >


      <Expand duration={750} placeholder={undefined} onPointerEnterCapture={undefined} onPointerLeaveCapture={undefined} />
    </button>
  );
};

export default ThemeToggle;
