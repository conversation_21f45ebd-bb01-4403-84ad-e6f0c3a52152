import React from 'react';
import { useScrollToTop } from '@/hooks/useScrollToTop';

interface ScrollToTopProps {
  behavior?: 'auto' | 'smooth';
  delay?: number;
  enabled?: boolean;
}

/**
 * Component that automatically scrolls to top when route changes
 * Place this component inside your Router to enable auto-scroll functionality
 */
const ScrollToTop: React.FC<ScrollToTopProps> = ({
  behavior = 'smooth',
  delay = 0,
  enabled = true
}) => {
  useScrollToTop({ behavior, delay, enabled });
  
  // This component doesn't render anything
  return null;
};

export default ScrollToTop;
