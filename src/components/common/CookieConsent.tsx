import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import { CookieStorage } from '@/utils/storage';
import Button from './Button';
import Card from './Card';
import { IoClose, IoSettings, IoShield } from 'react-icons/io5';

const CookieConsent: React.FC = () => {
  const { t } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Check if user has already given consent
    const hasConsent = CookieStorage.getCookieConsent();
    if (hasConsent === null) {
      // Show consent banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    CookieStorage.setCookieConsent(true);
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    CookieStorage.setCookieConsent(false);
    // Clear all existing cookies except essential ones
    CookieStorage.clearAllData();
    setIsVisible(false);
  };

  const handleCustomize = () => {
    setShowDetails(!showDetails);
  };

  const handleSavePreferences = () => {
    CookieStorage.setCookieConsent(true);
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto sm:max-w-lg sm:left-auto sm:right-4 sm:mx-0"
      >
        <Card variant="elevated" padding="lg" className="shadow-2xl border-2 border-primary/20">
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                  <IoShield className="w-5 h-5 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-headline">
                  {t('cookies.title')}
                </h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="p-1 -mt-1 -mr-1"
              >
                <IoClose className="w-4 h-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="space-y-3">
              <p className="text-sm text-body-text leading-relaxed">
                {t('cookies.description')}
              </p>

              {showDetails && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="space-y-3 border-t border-border pt-3"
                >
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-headline">
                      {t('cookies.types.essential.title')}
                    </h4>
                    <p className="text-xs text-caption">
                      {t('cookies.types.essential.description')}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-headline">
                      {t('cookies.types.functional.title')}
                    </h4>
                    <p className="text-xs text-caption">
                      {t('cookies.types.functional.description')}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-headline">
                      {t('cookies.types.analytics.title')}
                    </h4>
                    <p className="text-xs text-caption">
                      {t('cookies.types.analytics.description')}
                    </p>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Actions */}
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleAcceptAll}
                  className="flex-1"
                >
                  {t('cookies.actions.accept_all')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRejectAll}
                  className="flex-1"
                >
                  {t('cookies.actions.reject_all')}
                </Button>
              </div>
              
       

              {showDetails && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleSavePreferences}
                  className="w-full"
                >
                  {t('cookies.actions.save_preferences')}
                </Button>
              )}
            </div>

          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default CookieConsent;
