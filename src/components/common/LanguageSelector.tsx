import { useLanguage } from '@/hooks/useLanguage';
import { AnimatePresence, motion } from 'framer-motion';
import React, {
  forwardRef,
  KeyboardEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import ReactCountryFlag from 'react-country-flag';
import { IoChevronDown } from 'react-icons/io5';
import Button from './Button';

// Type for use in ref array
interface LanguageSelectorProps {
  className?: string;
}

const LanguageSelector = forwardRef<HTMLButtonElement, LanguageSelectorProps>(
  function LanguageSelector({ className }, buttonRef) {
    const { currentLanguage, changeLanguage, getSupportedLanguages } = useLanguage();
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);

    const supportedLanguages = useMemo(() => getSupportedLanguages(), [getSupportedLanguages]);

    // Focus management
    const optionsRef = useRef<Array<HTMLButtonElement | null>>([]);
    const triggerBtnRef = useRef<HTMLButtonElement | null>(null);

    // Merge forwardRef with local ref
    useEffect(() => {
      if (typeof buttonRef === 'function') {
        buttonRef(triggerBtnRef.current);
      } else if (buttonRef) {
        (buttonRef as React.MutableRefObject<HTMLButtonElement | null>).current = triggerBtnRef.current;
      }
    }, [buttonRef]);

    const getCurrentLanguageData = useCallback(() => (
      supportedLanguages.find(lang => lang.code === currentLanguage) || supportedLanguages[0]
    ), [supportedLanguages, currentLanguage]);

    const currentLang = getCurrentLanguageData();

    // Open dropdown, set highlight, and focus first item
    const handleOpen = () => {
      setIsOpen(true);
      setHighlightedIndex(
        supportedLanguages.findIndex(lang => lang.code === currentLanguage)
      );
    };

    // Close dropdown and return focus to trigger
    const handleClose = () => {
      setIsOpen(false);
      setHighlightedIndex(-1);
      triggerBtnRef.current?.focus();
    };

    // Handle async language change with loading feedback and close after
    const handleLanguageChange = async (languageCode: string) => {
      if (isLoading || !isOpen) return;
      setIsLoading(true);
      try {
        await changeLanguage(languageCode);
        setIsOpen(false);
      } catch (e) {
        // Could show an error message here
        // Optionally, keep dropdown open for retry
      } finally {
        setIsLoading(false);
      }
    };

    // Move focus among options
    const handleListKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
      // Prevent default scroll
      if (['ArrowDown', 'ArrowUp', 'Home', 'End'].includes(e.key)) {
        e.preventDefault();
      }

      if (e.key === 'ArrowDown') {
        setHighlightedIndex(i => {
          const next = i < supportedLanguages.length - 1 ? i + 1 : 0;
          return next;
        });
      }
      if (e.key === 'ArrowUp') {
        setHighlightedIndex(i => {
          const prev = i > 0 ? i - 1 : supportedLanguages.length - 1;
          return prev;
        });
      }
      if (e.key === 'Home') {
        setHighlightedIndex(0);
      }
      if (e.key === 'End') {
        setHighlightedIndex(supportedLanguages.length - 1);
      }
      if (e.key === 'Escape') {
        handleClose();
      }
      if (e.key === 'Enter' || e.key === ' ') {
        if (highlightedIndex >= 0) {
          handleLanguageChange(supportedLanguages[highlightedIndex].code);
        }
      }
    };

    // Focus highlighted option
    useEffect(() => {
      if (!isOpen) return;
      if (highlightedIndex >= 0 && optionsRef.current[highlightedIndex]) {
        optionsRef.current[highlightedIndex]?.focus();
      }
    }, [highlightedIndex, isOpen]);

    // Focus first item on open
    useEffect(() => {
      if (isOpen) {
        const idx = supportedLanguages.findIndex(x => x.code === currentLanguage);
        setHighlightedIndex(idx >= 0 ? idx : 0);
      }
    }, [isOpen, supportedLanguages, currentLanguage]);

    // Close on outside mousedown or blur
    useEffect(() => {
      if (!isOpen) return;
      function handleClickOutside(e: MouseEvent) {
        if (!triggerBtnRef.current?.contains(e.target as any) &&
          !optionsRef.current.some(ref => ref?.contains(e.target as Node))
        ) {
          setIsOpen(false);
        }
      }
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, [isOpen]);

    return (
      <div className={`relative ${className || ''}`}>
        <Button
          ref={triggerBtnRef}
          variant="ghost"
          size="sm"
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-controls="language-selector-dropdown"
          onClick={() => {
            if (isOpen) {
              handleClose();
            } else {
              handleOpen();
            }
          }}
          className="relative p-2 gap-0 w-[200px] bg-card border flex justify-between"
          rightIcon={
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <IoChevronDown size={14} />
            </motion.div>
          }
          disabled={isLoading}
        >
          <ReactCountryFlag
            countryCode={currentLang.countryCode}
            svg
            style={{ width: '1.2em', height: '1.2em', borderRadius: '2px' }}
            title={currentLang.name}
          />
          <span className="text-xs font-medium ml-2">
            {currentLang.name}
          </span>
        </Button>

        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 z-40"
                aria-hidden="true"
                tabIndex={-1}
                onClick={handleClose}
              />
              {/* Dropdown */}
              <motion.div
                id="language-selector-dropdown"
                role="listbox"
                aria-activedescendant={
                  highlightedIndex >= 0
                    ? `selector-option-${supportedLanguages[highlightedIndex].code}`
                    : undefined
                }
                className="absolute right-0 top-full mt-2 z-50 bg-card border border-border rounded-lg shadow-custom-lg overflow-hidden min-w-[160px]"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.15 }}
                onKeyDown={handleListKeyDown}
                tabIndex={-1}
              >
                {supportedLanguages.map((language, idx) => (
                  <motion.button
                    key={language.code}
                    id={`selector-option-${language.code}`}
                    ref={el => (optionsRef.current[idx] = el)}
                    type="button"
                    role="option"
                    aria-selected={currentLanguage === language.code}
                    tabIndex={highlightedIndex === idx ? 0 : -1}
                    disabled={isLoading}
                    className={`w-full text-left px-4 py-3 text-sm hover:bg-surface-variant transition-colors focus:outline-primary
                      ${currentLanguage === language.code
                        ? 'bg-primary/10 text-primary font-medium'
                        : 'text-body-text'
                      }
                      ${highlightedIndex === idx ? 'ring-2 ring-primary/30' : ''}
                    `}
                    onClick={e => {
                      e.stopPropagation();
                      handleLanguageChange(language.code);
                    }}
                    onMouseEnter={() => setHighlightedIndex(idx)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <ReactCountryFlag
                          countryCode={language.countryCode}
                          svg
                          style={{
                            width: '1.2em',
                            height: '1.2em',
                            borderRadius: '2px'
                          }}
                          title={language.name}
                        />
                        <div>
                          <div>{language.nativeName || language.name}</div>
                          <div className="text-xs text-caption">
                            {language.name}
                          </div>
                        </div>
                      </div>
                      {currentLanguage === language.code && (
                        <motion.div
                          className="w-2 h-2 bg-primary rounded-full"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.2 }}
                        />
                      )}
                      {isLoading && highlightedIndex === idx && (
                        <div
                          className="ml-2 animate-spin rounded-full h-3 w-3 border-b-2 border-primary"
                          aria-label="Loading"
                        ></div>
                      )}
                    </div>
                  </motion.button>
                ))}
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

export default LanguageSelector;
