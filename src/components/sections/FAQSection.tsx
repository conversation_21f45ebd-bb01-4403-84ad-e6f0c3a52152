import Card from '@/components/common/Card';
import { motion, Variants } from 'framer-motion';
import React from 'react';
import ThemeAwareIllustration from '../assets/ThemeAwareIllustration';

// Types
export interface FAQSectionProps {
  t: (key: string) => string;
  cardVariant?: 'elevated' | 'outlined' | string;
  cardPadding?: 'lg' | 'md' | string;
  faqCount?: number;
}

interface FAQ {
  question: string;
  answer: string;
}

// Animation Variants
const sectionVariants: Variants = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.7, ease: 'easeOut' } },
};

const faqListVariants: Variants = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.13 } },
};

const faqItemVariants: Variants = {
  hidden: { opacity: 0, y: 24 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: 'easeOut' } },
};

const FAQSection: React.FC<FAQSectionProps> = ({
  t,
  cardVariant = 'elevated',
  cardPadding = 'lg',
  faqCount = 5,
}) => {
  const faqs: FAQ[] = Array.from({ length: faqCount }, (_, i) => ({
    question: t(`pages.home.faq.items.${i}.question`),
    answer: t(`pages.home.faq.items.${i}.answer`),
  }));

  return (
    <motion.section
      id="faq"
      className="section"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-56px' }}
      variants={sectionVariants}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl ">
            <ThemeAwareIllustration number={3}  className='h-[200px] mx-auto w-full' />

        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('pages.home.faq_title')}
          </h2>
          <p className="text-xl text-body-text">
            {t('pages.home.faq_subtitle')}
          </p>
        </div>
        <motion.dl
          className="space-y-6"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={faqListVariants}
        >
          {faqs.map((faq, idx) => (
            <motion.div
              key={idx}
              variants={faqItemVariants}
              className="focus:outline-none"
              tabIndex={0}
              aria-labelledby={`faq-question-${idx}`}
            >
              <Card
                variant={cardVariant}
                padding={cardPadding}
                className="focus:ring-2 focus:ring-primary focus:ring-offset-2"
              >
                <dt>
                  <h3
                    id={`faq-question-${idx}`}
                    className="text-lg font-semibold text-headline mb-3"
                  >
                    {faq.question}
                  </h3>
                </dt>
                <dd>
                  <p className="text-body-text leading-relaxed">{faq.answer}</p>
                </dd>
              </Card>
            </motion.div>
          ))}
        </motion.dl>
      </div>
    </motion.section>
  );
};

export default FAQSection;
