import Card from '@/components/common/Card';
import { motion, Variants } from 'framer-motion';
import React from 'react';
import { IoAnalytics, IoFlash, IoShield } from 'react-icons/io5';

// --- Types and interfaces ---

export interface FeaturesSectionProps {
  t: (key: string) => string;
  cardVariant?: 'elevated' | 'outlined' | string;
  cardPadding?: 'lg' | 'md' | string;
}

interface FeatureItem {
  icon: React.ReactNode;
  title: string;
  description: string;
}

// --- Animation variants ---

const cardVariants: Variants = {
  hidden: { opacity: 0, y: 32 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      delay: i * 0.15,
      ease: 'easeOut',
    },
  }),
};

const sectionVariants: Variants = {
  hidden: { opacity: 0, y: 24 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.7,
      ease: 'easeOut',
    },
  },
};

// --- Component ---

const FeaturesSection: React.FC<FeaturesSectionProps> = ({
  t,
  cardVariant = 'elevated',
  cardPadding = 'lg',
}) => {
  const features: FeatureItem[] = [
    {
      icon: <IoAnalytics className="w-8 h-8" aria-label={t('pages.home.highlights.items.0.title')} />,
      title: t('pages.home.highlights.items.0.title'),
      description: t('pages.home.highlights.items.0.desc'),
    },
    {
      icon: <IoFlash className="w-8 h-8" aria-label={t('pages.home.highlights.items.1.title')} />,
      title: t('pages.home.highlights.items.1.title'),
      description: t('pages.home.highlights.items.1.desc'),
    },
    {
      icon: <IoShield className="w-8 h-8" aria-label={t('pages.home.highlights.items.2.title')} />,
      title: t('pages.home.highlights.items.2.title'),
      description: t('pages.home.highlights.items.2.desc'),
    }
  ];

  return (
    <motion.section
      id="features"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-48px' }}
      variants={sectionVariants}
      className="section bg-surface/30"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('pages.home.highlights_title')}
          </h2>
          <p className="text-xl text-body-text max-w-2xl mx-auto">
            {t('pages.home.highlights_subtitle')}
          </p>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, i) => (
            <motion.div
              key={i}
              custom={i}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={cardVariants}
            >
              <Card variant={cardVariant} padding={cardPadding} className="text-center h-full">
                <div className="mb-6">
                  <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-primary flex items-center justify-center text-white">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-headline mb-4">
                  {feature.title}
                </h3>
                <p className="text-body-text leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default FeaturesSection;
