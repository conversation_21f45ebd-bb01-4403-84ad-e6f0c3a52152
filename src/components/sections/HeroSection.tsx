import Button from '@/components/common/Button';
import i18n from '@/i18n/config';
import { motion } from 'framer-motion';
import React from 'react';
import { IoCloudUpload, IoPlay } from 'react-icons/io5';
import ThemeAwareIllustration from '../assets/ThemeAwareIllustration';

interface HeroSectionProps {
  t: (key: string) => string;
  navigate: ReturnType<typeof import('react-router-dom').useNavigate>;
}

const HeroSection: React.FC<HeroSectionProps> = ({ t, navigate }) => {
  const dir = i18n.dir();
  // Removed illustrationInitialX since we no longer animate X position
  const illustrationClass = `w-full mx-auto max-md:hidden h-auto max-w-lg${dir === 'rtl' ? ' scale-x-[-1]' : ''}`;
  const illustrationPositionClass =
    dir === 'rtl'
      ? 'lg:absolute lg:left-0 lg:top-0 lg:bottom-0 lg:h-auto'
      : 'lg:absolute lg:right-0 lg:top-0 lg:bottom-0 lg:h-auto';

  const headingLineHeight = dir === 'rtl' ? '70px' : '60px';

  return (
    <section dir={dir} className="relative section overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5" />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className={`text-center ${dir === 'rtl' ? 'lg:text-right' : 'lg:text-left'}`}>
            <motion.h1
            dir={dir}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-headline mb-6"
              style={{ lineHeight: headingLineHeight }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {t('hero.headline')}
            </motion.h1>
            <motion.p
            dir={dir}

              className="text-xl text-body-text mb-8 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              {t('hero.subheadline')}
            </motion.p>
            <motion.div
              className={`flex justify-center items-center gap-4 flex-wrap ${dir === 'rtl' ? 'lg:justify-end flex-row-reverse' : 'lg:justify-start'
                }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Button
                variant="primary"
                size="lg"
                onClick={() => navigate('/upload')}
                leftIcon={<IoCloudUpload />}
                className="px-8 py-4 text-lg font-semibold"
              >
                {t('hero.cta_primary')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => navigate('/learn-more')}
                leftIcon={<IoPlay />}
                className="px-8 py-4 text-lg font-semibold"
              >
                {t('hero.cta_secondary')}
              </Button>
            </motion.div>
          </div>
          {/* CHANGE: opacity fade only, no X animation */}
          <motion.div
            className={`relative ${illustrationPositionClass}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <ThemeAwareIllustration number={1} className={illustrationClass} />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
