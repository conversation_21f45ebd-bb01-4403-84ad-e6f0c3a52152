import { motion, Variants } from 'framer-motion';
import React from 'react';
import { IoCloudUploadOutline, IoDocumentTextOutline, IoSparklesOutline } from 'react-icons/io5';

export interface HowItWorksSectionProps {
  t: (key: string) => string;
}

interface Step {
  number: string;
  title: string;
  description: string;
  illustration: string;
  fallbackIcon: React.ReactNode;
}

// Best high-res SVGs from a trusted CDN:
const illustrations = [
  "https://illustrations.popsy.co/amber/file-upload.svg",
  "https://illustrations.popsy.co/amber/artificial-intelligence.svg",
  "https://illustrations.popsy.co/amber/report.svg",
];

// Matching crisp outline icons as fallback:
const fallbackIcons = [
  <IoCloudUploadOutline className="w-20 h-20 mx-auto mb-4 text-primary" aria-hidden="true" focusable="false" />,
  <IoSparklesOutline className="w-20 h-20 mx-auto mb-4 text-primary" aria-hidden="true" focusable="false" />,
  <IoDocumentTextOutline className="w-20 h-20 mx-auto mb-4 text-primary" aria-hidden="true" focusable="false" />,
];

// Animation variants
const sectionVariants: Variants = {
  hidden: { opacity: 0, y: 32 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.7, ease: 'easeOut' } },
};

const stepVariants: Variants = {
  hidden: { opacity: 0, y: 36 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, delay: 0.15 + i * 0.12, ease: 'easeOut' },
  }),
};

const HowItWorksSection: React.FC<HowItWorksSectionProps> = ({ t }) => {
  const steps: Step[] = [0, 1, 2].map((idx) => ({
    number: `0${idx + 1}`,
    title: t(`pages.home.howitworks.steps.${idx}.title`),
    description: t(`pages.home.howitworks.steps.${idx}.desc`),
    illustration: illustrations[idx],
    fallbackIcon: fallbackIcons[idx],
  }));

  return (
    <motion.section
      id="how-it-works"
      className="section bg-surface/30"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-64px' }}
      variants={sectionVariants}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('pages.home.howitworks_title')}
          </h2>
          <p className="text-xl text-body-text max-w-2xl mx-auto">
            {t('pages.home.howitworks_subtitle')}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, idx) => (
            <motion.div
              key={idx}
              className="relative text-center"
              custom={idx}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={stepVariants}
            >
              <div className="relative mb-6">
                <picture>
                  {/* Best SVG; fallback to icon (hidden by default, shown if img fails) */}
                  <source srcSet={step.illustration} type="image/svg+xml" />
                  <img
                    src={step.illustration}
                    alt=""
                    role="presentation"
                    className="w-20 h-20 mx-auto mb-4"
                    onError={(e) => {
                      // Hide broken image, show icon fallback
                      e.currentTarget.style.display = 'none';
                      const icon = e.currentTarget.nextElementSibling as HTMLElement;
                      if (icon) icon.style.display = '';
                    }}
                  />
                </picture>
                <span style={{ display: 'none' }}>
                  {step.fallbackIcon}
                </span>
                <div className="w-16 h-16 mx-auto rounded-full bg-gradient-primary text-white flex items-center justify-center text-xl font-bold">
                  {step.number}
                </div>
                {idx < steps.length - 1 && (
                  <div
                    className="hidden md:block absolute top-8 left-1/2 w-full h-0.5 bg-border transform translate-x-8"
                    aria-hidden="true"
                  />
                )}
              </div>
              <h3 className="text-xl font-semibold text-headline mb-3">
                {step.title}
              </h3>
              <p className="text-body-text">{step.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default HowItWorksSection;
