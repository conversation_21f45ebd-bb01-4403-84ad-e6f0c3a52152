import Button from '@/components/common/Button';
import { motion, Variants } from 'framer-motion';
import React from 'react';
import { IoArrowForward } from 'react-icons/io5';
import ThemeAwareIllustration from '../assets/ThemeAwareIllustration';

// --- Types ---
export interface CTASectionProps {
  t: (key: string) => string;
  navigate: ReturnType<typeof import('react-router-dom').useNavigate>;
}

// --- Animation ---
const sectionVariants: Variants = {
  hidden: { opacity: 0, y: 0 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.65, ease: 'easeOut' } },
};

const CTASection: React.FC<CTASectionProps> = ({ t, navigate }) => (
  <motion.section
    className="section bg-gradient-to-br from-primary/5 to-accent/5 relative"
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true, margin: '0px' }}
    variants={sectionVariants}
  >
    <ThemeAwareIllustration number={6} className='h-[200px] w-full mx-auto' />

    <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
      <div className="text-center max-w-4xl mx-auto">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-headline mb-4 sm:mb-6 leading-tight">
          {t('pages.home.cta_section.title')}
        </h2>
        <p className="text-lg sm:text-xl text-body-text mb-6 sm:mb-8 leading-relaxed px-2">
          {t('pages.home.cta_section.description')}
        </p>
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-stretch sm:items-center max-w-md sm:max-w-none mx-auto">
          <Button
            variant="primary"
            size="lg"
            onClick={() => navigate('/upload')}
            leftIcon={<IoArrowForward />}
            className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold min-h-[48px] sm:min-h-[56px]"
          >
            {t('pages.home.cta_section.primary')}
          </Button>
          <Button
            variant="outline"
            size="lg"
            onClick={() => navigate('/learn-more')}
            className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold min-h-[48px] sm:min-h-[56px]"
          >
            {t('pages.home.cta_section.secondary')}
          </Button>
        </div>
      </div>
    </div>
  </motion.section>
);

export default CTASection;
