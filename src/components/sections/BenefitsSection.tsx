import { motion, Variants } from 'framer-motion';
import React from 'react';
import { IoAnalytics, IoCheckmark } from 'react-icons/io5';
import ThemeAwareIllustration from '../assets/ThemeAwareIllustration';

// --- Types ---
export interface BenefitsSectionProps {
  t: (key: string) => string;
}

interface BenefitItem {
  icon: React.ReactNode;
  title: string;
  description: string;
}

// --- Formal Scroll Animations ---
const columnVariants: Variants = {
  hiddenLeft: { opacity: 0, x: 0 },
  hiddenRight: { opacity: 0, x: 0 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.6, ease: 'easeOut' } },
};

const benefitItemVariants: Variants = {
  hidden: { opacity: 0, y: 24 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, delay: 0.15 + i * 0.1, ease: 'easeOut' },
  }),
};

// --- Main Component ---
const BenefitsSection: React.FC<BenefitsSectionProps> = ({ t }) => {
  const benefits: BenefitItem[] = [
    {
      icon: (
        <IoCheckmark className="w-6 h-6 text-success" aria-hidden="true" focusable="false" />
      ),
      title: t('pages.home.benefits.items.0.title'),
      description: t('pages.home.benefits.items.0.desc'),
    },
    {
      icon: (
        <IoCheckmark className="w-6 h-6 text-success" aria-hidden="true" focusable="false" />
      ),
      title: t('pages.home.benefits.items.1.title'),
      description: t('pages.home.benefits.items.1.desc'),
    },
    {
      icon: (
        <IoCheckmark className="w-6 h-6 text-success" aria-hidden="true" focusable="false" />
      ),
      title: t('pages.home.benefits.items.2.title'),
      description: t('pages.home.benefits.items.2.desc'),
    },
    {
      icon: (
        <IoCheckmark className="w-6 h-6 text-success" aria-hidden="true" focusable="false" />
      ),
      title: t('pages.home.benefits.items.3.title'),
      description: t('pages.home.benefits.items.3.desc'),
    },
  ];

  return (
    <section id="benefits" className="section">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column: Benefits List */}
          <motion.div
            initial="hiddenLeft"
            whileInView="visible"
            viewport={{ once: true, margin: '-64px' }}
            variants={columnVariants}
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-headline mb-6">
              {t('pages.home.benefits_title')}
            </h2>
            <p className="text-xl text-body-text mb-8 leading-relaxed">
              {t('pages.home.benefits_subtitle')}
            </p>
            <div className="space-y-4">
              {benefits.map((benefit, idx) => (
                <motion.div
                  key={idx}
                  className="flex items-start space-x-4"
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  custom={idx}
                  variants={benefitItemVariants}
                >
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-success/10 flex items-center justify-center">
                    {benefit.icon}
                    <span className="sr-only">
                      {t('pages.home.benefits.checked_label', { title: benefit.title })}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-headline mb-1">{benefit.title}</h4>
                    <p className="text-body-text">{benefit.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
          {/* Right Column: Highlight Block */}
          <motion.div
            className="relative"
            initial="hiddenRight"
            whileInView="visible"
            viewport={{ once: true }}
            variants={columnVariants}
          >
            <div className="aspect-square rounded-3xl h-full p-8 flex items-center justify-center">
            <ThemeAwareIllustration className='h-full w-full max-md:w-[90%] ' number={5}  />
         
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
