import React from 'react';
import { motion } from 'framer-motion';
import Card from '@/components/common/Card';
import { IoAnalytics, IoShield, IoFlash } from 'react-icons/io5';

interface HighlightsSectionProps {
  t: (key: string) => string;
}

const HighlightsSection: React.FC<HighlightsSectionProps> = ({ t }) => {
  const highlights = [
    {
      icon: <IoAnalytics className="w-8 h-8" />,
      title: t('pages.home.highlights.items.0.title'),
      description: t('pages.home.highlights.items.0.desc'),
      metric: t('pages.home.highlights.items.0.metric'),
    },
    {
      icon: <IoShield className="w-8 h-8" />,
      title: t('pages.home.highlights.items.1.title'),
      description: t('pages.home.highlights.items.1.desc'),
      metric: t('pages.home.highlights.items.1.metric'),
    },
    {
      icon: <IoFlash className="w-8 h-8" />,
      title: t('pages.home.highlights.items.2.title'),
      description: t('pages.home.highlights.items.2.desc'),
      metric: t('pages.home.highlights.items.2.metric'),
    },
  ];

  return (
    <section className="py-20 bg-surface/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('pages.home.highlights_title')}
          </h2>
          <p className="text-xl text-body-text max-w-2xl mx-auto">
            {t('pages.home.highlights_subtitle')}
          </p>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {highlights.map((highlight, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: idx * 0.1 }}
              viewport={{ once: true }}
            >
              <Card variant="elevated" padding="lg" className="h-full text-center">
                <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-primary flex items-center justify-center text-white">
                  {highlight.icon}
                </div>
                <h3 className="text-xl font-semibold text-headline mb-4">
                  {highlight.title}
                </h3>
                <p className="text-body-text mb-4 leading-relaxed">
                  {highlight.description}
                </p>
                <div className="text-2xl font-bold text-primary">
                  {highlight.metric}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HighlightsSection;
