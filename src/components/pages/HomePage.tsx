import React from 'react';
import { motion } from 'framer-motion';
import { IoAnalytics, IoFlash, IoShield, IoArrowForward } from 'react-icons/io5';
import { useLanguage } from '@/hooks/useLanguage';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';

interface HomePageProps {
  onGetStarted: () => void;
}

const HomePage: React.FC<HomePageProps> = ({ onGetStarted }) => {
  const { t } = useLanguage();

  const features = [
    {
      icon: <IoAnalytics className="w-8 h-8" />,
      title: t('hero.features.ai_powered'),
      description: t('hero.features.ai_description')
    },
    {
      icon: <IoFlash className="w-8 h-8" />,
      title: t('hero.features.instant_results'),
      description: t('hero.features.instant_description')
    },
    {
      icon: <IoShield className="w-8 h-8" />,
      title: t('hero.features.secure_private'),
      description: t('hero.features.secure_description')
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 300,
        damping: 24
      }
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="text-center space-y-8"
          >
            {/* Headline */}
            <motion.div variants={itemVariants} className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-headline leading-tight">
                {t('hero.headline')}
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-body-text max-w-4xl mx-auto leading-relaxed">
                {t('hero.subheadline')}
              </p>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                size="lg"
                onClick={onGetStarted}
                rightIcon={<IoArrowForward />}
                className="w-full sm:w-auto min-w-[200px] shadow-lg hover:shadow-xl"
              >
                {t('hero.cta_primary')}
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto min-w-[200px]"
              >
                {t('hero.cta_secondary')}
              </Button>
            </motion.div>

            {/* Hero Illustration */}
            <motion.div
              variants={itemVariants}
              className="relative max-w-4xl mx-auto mt-16"
            >
              <div className="relative bg-gradient-hero rounded-2xl p-8 shadow-custom-xl overflow-hidden">
                {/* Animated elements */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {[1, 2, 3].map((i) => (
                    <motion.div
                      key={i}
                      className="bg-[hsl(var(--white-alpha-10))] backdrop-blur-sm rounded-lg p-4 h-24"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + i * 0.1, duration: 0.5 }}
                    >
                      <div className="h-full bg-gradient-primary/20 rounded animate-pulse-scale" />
                    </motion.div>
                  ))}
                </div>
                
                {/* Floating elements */}
                <motion.div
                  className="absolute top-4 right-4 w-16 h-16 bg-primary/20 rounded-full"
                  animate={{ 
                    rotate: 360,
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ 
                    rotate: { duration: 20, repeat: Infinity, ease: 'linear' },
                    scale: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
                  }}
                />
                
                <motion.div
                  className="absolute bottom-4 left-4 w-12 h-12 bg-secondary/20 rounded-lg"
                  animate={{ 
                    y: [-10, 10, -10],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-surface/50">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            className="space-y-12"
          >
            <motion.div variants={itemVariants} className="text-center">
              <h2 className="text-3xl font-bold text-headline mb-4">
                {t('pages.home.features_title')}
              </h2>
              <p className="text-lg text-body-text max-w-2xl mx-auto">
                {t('app.description')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ y: -5 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Card 
                    variant="elevated" 
                    padding="lg"
                    className="h-full text-center hover:shadow-custom-xl transition-all duration-base"
                  >
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center text-[hsl(var(--white))]">
                        {feature.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-headline mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-body-text leading-relaxed">
                      {feature.description}
                    </p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-5xl">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            className="text-center space-y-8"
          >
            <motion.div variants={itemVariants}>
              <h2 className="text-3xl font-bold text-headline mb-4">
                {t('pages.home.cta_section.title')}
              </h2>
              <p className="text-lg text-body-text mb-8">
                {t('pages.home.cta_section.description')}
              </p>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Button
                size="xl"
                onClick={onGetStarted}
                rightIcon={<IoArrowForward />}
                className="px-12"
              >
                {t('hero.cta_primary')}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
