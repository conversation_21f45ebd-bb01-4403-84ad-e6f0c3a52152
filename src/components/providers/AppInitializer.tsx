import React, { useEffect } from 'react';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { initPerformanceMonitoring } from '@/utils/performance';
import FontManager from '@/utils/fontManager';
import { CookieStorage } from '@/utils/storage';

/**
 * Component to initialize app-wide functionality
 * This includes performance monitoring, keyboard shortcuts, etc.
 */
const AppInitializer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  useEffect(() => {
    const initializeApp = async () => {
      // Initialize performance monitoring
      initPerformanceMonitoring();

      // Initialize font system with saved language
      const savedLanguage = CookieStorage.getLanguage();
      await FontManager.initialize(savedLanguage);

      // Log app initialization in development
      if (import.meta.env.DEV) {
        console.log('🚀 4CV App initialized with enhanced features:');
        console.log('✅ Performance monitoring active');
        console.log('✅ Keyboard shortcuts enabled');
        console.log('✅ Language-specific fonts loaded');
        console.log('✅ Scroll animations ready');
        console.log('✅ Gesture handling available');
        console.log('✅ Success celebrations ready');
      }
    };

    initializeApp();
  }, []);

  return <>{children}</>;
};

export default AppInitializer;
