import React from 'react';
import { useTheme } from 'next-themes';

// Import light theme illustrations
import LightIllustration1 from './light/Illustration1';
import LightIllustration2 from './light/Illustration2';
import LightIllustration3 from './light/Illustration3';
import LightIllustration4 from './light/Illustration4';
import LightIllustration5 from './light/Illustration5';
import LightIllustration6 from './light/Illustration6';

// Import dark theme illustrations
import DarkIllustration1 from './dark/Illustration1';
import DarkIllustration2 from './dark/Illustration2';
import DarkIllustration3 from './dark/Illustration3';
import DarkIllustration4 from './dark/Illustration4';
import DarkIllustration5 from './dark/Illustration5';
import DarkIllustration6 from './dark/Illustration6';

// Define illustration mapping
const lightIllustrations = {
  1: LightIllustration1,
  2: LightIllustration2,
  3: LightIllustration3,
  4: LightIllustration4,
  5: LightIllustration5,
  6: LightIllustration6,
} as const;

const darkIllustrations = {
  1: DarkIllustration1,
  2: DarkIllustration2,
  3: DarkIllustration3,
  4: DarkIllustration4,
  5: DarkIllustration5,
  6: DarkIllustration6,
} as const;

export type IllustrationNumber = keyof typeof lightIllustrations;

export interface ThemeAwareIllustrationProps extends React.SVGAttributes<SVGSVGElement> {
  /** The illustration number (1-6) */
  number: IllustrationNumber;
  /** Optional className for styling */
  className?: string;
}

/**
 * Theme-aware illustration component that automatically selects the appropriate
 * illustration based on the current theme (light/dark).
 * 
 * @param number - The illustration number (1-6)
 * @param className - Optional CSS classes
 * @param props - Additional SVG props
 */
const ThemeAwareIllustration = React.forwardRef<SVGSVGElement, ThemeAwareIllustrationProps>(
  ({ number, className, ...props }, ref) => {
    const { theme, resolvedTheme } = useTheme();

    // Use resolvedTheme to get the actual theme (light/dark) when theme is 'system'
    const effectiveTheme = resolvedTheme || theme;

    // Select the appropriate illustration based on theme
    const IllustrationComponent = effectiveTheme === 'dark'
      ? darkIllustrations[number]
      : lightIllustrations[number];

    if (!IllustrationComponent) {
      console.warn(`Illustration ${number} not found for theme ${effectiveTheme}`);
      return null;
    }

    return (
      <IllustrationComponent
        ref={ref}
        className={className}
        {...props}
      />
    );
  }
);

ThemeAwareIllustration.displayName = 'ThemeAwareIllustration';

export default ThemeAwareIllustration;
