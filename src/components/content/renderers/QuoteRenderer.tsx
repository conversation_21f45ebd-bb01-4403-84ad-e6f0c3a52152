import React from 'react';
import { QuoteSection } from '@/types/content';
import { motion } from 'framer-motion';
import { Quote } from 'lucide-react';

interface QuoteRendererProps {
  section: QuoteSection;
}

export const QuoteRenderer: React.FC<QuoteRendererProps> = ({ section }) => {
  const { text, author, role, company } = section.content;

  return (
    <motion.section
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ duration: 0.6 }}
      className="mb-12"
    >
      <div className="relative bg-gradient-to-br from-primary/5 to-primary/10 rounded-2xl p-8 md:p-12 border border-primary/20">
        <Quote className="absolute top-6 left-6 text-4xl text-primary/30" />
        
        <blockquote className="relative z-10">
          <p className="text-xl md:text-2xl text-headline font-medium italic leading-relaxed mb-6 pl-8">
            "{text}"
          </p>
          
          <footer className="flex items-center space-x-4 pl-8">
            <div>
              <cite className="text-lg font-semibold text-headline not-italic">
                {author}
              </cite>
              {(role || company) && (
                <p className="text-body-text">
                  {role}{role && company && ', '}{company}
                </p>
              )}
            </div>
          </footer>
        </blockquote>
      </div>
    </motion.section>
  );
};
