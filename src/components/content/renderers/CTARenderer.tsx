import React from 'react';
import { CTASection } from '@/types/content';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

interface CTARendererProps {
  section: CTASection;
}

export const CTARenderer: React.FC<CTARendererProps> = ({ section }) => {
  const { heading, description, primaryButton, secondaryButton } = section.content;

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ duration: 0.6 }}
      className="mb-12"
    >
      <div className="text-center bg-gradient-to-br from-primary/5 to-primary/10 rounded-2xl p-8 md:p-12 border border-primary/20">
        <h2 className="text-3xl md:text-4xl font-bold text-headline mb-4">
          {heading}
        </h2>
        
        <p className="text-lg text-body-text mb-8 max-w-2xl mx-auto leading-relaxed">
          {description}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to={primaryButton.href}
              className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-primary rounded-lg shadow-lg hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all"
            >
              {primaryButton.text}
            </Link>
          </motion.div>
          
          {secondaryButton && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to={secondaryButton.href}
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary bg-transparent border-2 border-primary rounded-lg hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all"
              >
                {secondaryButton.text}
              </Link>
            </motion.div>
          )}
        </div>
      </div>
    </motion.section>
  );
};
