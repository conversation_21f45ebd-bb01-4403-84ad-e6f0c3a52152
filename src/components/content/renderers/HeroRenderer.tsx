import React from 'react';
import { HeroSection } from '@/types/content';
import { motion } from 'framer-motion';

interface HeroRendererProps {
  section: HeroSection;
}

export const HeroRenderer: React.FC<HeroRendererProps> = ({ section }) => {
  const { title, subtitle, highlight } = section.content;

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="text-center mb-12"
    >
      {highlight && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="inline-block px-4 py-2 mb-6 text-sm font-medium text-primary bg-primary/10 rounded-full border border-primary/20"
        >
          {highlight}
        </motion.div>
      )}
      
      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
        className="text-5xl md:text-6xl font-bold text-headline mb-6 leading-tight"
      >
        {title}
      </motion.h1>
      
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.6 }}
        className="text-xl md:text-2xl text-body-text max-w-3xl mx-auto leading-relaxed"
      >
        {subtitle}
      </motion.p>
    </motion.section>
  );
};
