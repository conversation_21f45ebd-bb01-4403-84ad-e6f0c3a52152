import React from 'react';
import { TextSection } from '@/types/content';
import { motion } from 'framer-motion';

interface TextRendererProps {
  section: TextSection;
}

export const TextRenderer: React.FC<TextRendererProps> = ({ section }) => {
  const { heading, subheading, paragraphs } = section.content;

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ duration: 0.6 }}
      className="mb-12"
    >
      {heading && (
        <h2 className="text-3xl md:text-4xl font-bold text-headline mb-4 border-b border-border pb-3">
          {heading}
        </h2>
      )}
      
      {subheading && (
        <h3 className="text-2xl font-semibold text-headline mb-6 mt-8">
          {subheading}
        </h3>
      )}
      
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => (
          <motion.p
            key={index}
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            className="text-lg text-body-text leading-relaxed"
          >
            {paragraph}
          </motion.p>
        ))}
      </div>
    </motion.section>
  );
};
