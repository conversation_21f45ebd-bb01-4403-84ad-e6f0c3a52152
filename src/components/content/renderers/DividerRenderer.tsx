import React from 'react';
import { DividerSection } from '@/types/content';
import { motion } from 'framer-motion';

interface DividerRendererProps {
  section: DividerSection;
}

export const DividerRenderer: React.FC<DividerRendererProps> = ({ section }) => {
  const { style = 'line' } = section.content;

  const renderDivider = () => {
    switch (style) {
      case 'dots':
        return (
          <div className="flex justify-center space-x-2">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: i * 0.1, duration: 0.3 }}
                className="w-2 h-2 bg-border rounded-full"
              />
            ))}
          </div>
        );
      case 'wave':
        return (
          <div className="flex justify-center">
            <svg
              width="100"
              height="20"
              viewBox="0 0 100 20"
              className="text-border"
              fill="currentColor"
            >
              <path d="M0,10 Q25,0 50,10 T100,10" stroke="currentColor" strokeWidth="2" fill="none" />
            </svg>
          </div>
        );
      default:
        return (
          <motion.hr
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="border-border"
          />
        );
    }
  };

  return (
    <motion.section
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
      className="my-12"
    >
      {renderDivider()}
    </motion.section>
  );
};
