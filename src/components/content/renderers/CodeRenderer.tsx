import React from 'react';
import { CodeSection } from '@/types/content';
import { motion } from 'framer-motion';
import { IoCopyOutline } from 'react-icons/io5';

interface CodeRendererProps {
  section: CodeSection;
}

export const CodeRenderer: React.FC<CodeRendererProps> = ({ section }) => {
  const { heading, language, code, description } = section.content;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ duration: 0.6 }}
      className="mb-12"
    >
      {heading && (
        <h3 className="text-2xl md:text-3xl font-bold text-headline mb-4">
          {heading}
        </h3>
      )}
      
      {description && (
        <p className="text-body-text mb-6">{description}</p>
      )}
      
      <div className="relative bg-surface border border-border rounded-lg overflow-hidden">
        <div className="flex items-center justify-between px-4 py-3 bg-surface/80 border-b border-border">
          <span className="text-sm font-medium text-caption">{language}</span>
          <button
            onClick={handleCopy}
            className="flex items-center space-x-2 text-sm text-caption hover:text-primary transition-colors"
          >
            <IoCopyOutline className="w-4 h-4" />
            <span>Copy</span>
          </button>
        </div>
        
        <pre className="p-4 overflow-x-auto text-sm font-mono text-accent leading-relaxed">
          <code>{code}</code>
        </pre>
      </div>
    </motion.section>
  );
};
