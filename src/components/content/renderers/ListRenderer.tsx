import React from 'react';
import { ListSection } from '@/types/content';
import { motion } from 'framer-motion';

interface ListRendererProps {
  section: ListSection;
}

export const ListRenderer: React.FC<ListRendererProps> = ({ section }) => {
  const { heading, items, ordered } = section.content;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-50px' }}
      transition={{ duration: 0.6 }}
      className="mb-12"
    >
      {heading && (
        <h3 className="text-2xl md:text-3xl font-bold text-headline mb-8">
          {heading}
        </h3>
      )}
      
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className={`grid gap-6 ${ordered ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}`}
      >
        {items.map((item, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className={`
              ${ordered ? 'flex items-start space-x-4 p-6' : 'p-6'}
              bg-surface/50 rounded-lg border border-border hover:border-primary/30 transition-colors
            `}
          >
            {ordered && (
              <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                {index + 1}
              </div>
            )}
            
            <div className={`${ordered ? 'flex-1' : ''}`}>
              <div className="flex items-center space-x-3 mb-3">
                {item.icon && !ordered && (
                  <span className="text-2xl">{item.icon}</span>
                )}
                <h4 className="text-xl font-semibold text-headline">
                  {item.title}
                </h4>
              </div>
              
              <p className="text-body-text leading-relaxed">
                {item.description}
              </p>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </motion.section>
  );
};
