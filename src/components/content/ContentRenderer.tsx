import React from 'react';
import { ContentSection } from '@/types/content';
import { HeroRenderer } from './renderers/HeroRenderer';
import { TextRenderer } from './renderers/TextRenderer';
import { ListRenderer } from './renderers/ListRenderer';
import { QuoteRenderer } from './renderers/QuoteRenderer';
import { CodeRenderer } from './renderers/CodeRenderer';
import { CTARenderer } from './renderers/CTARenderer';
import { DividerRenderer } from './renderers/DividerRenderer';

interface ContentRendererProps {
  section: ContentSection;
}

export const ContentRenderer: React.FC<ContentRendererProps> = ({ section }) => {
  switch (section.type) {
    case 'hero':
      return <HeroRenderer section={section} />;
    case 'text':
      return <TextRenderer section={section} />;
    case 'list':
      return <ListRenderer section={section} />;
    case 'quote':
      return <QuoteRenderer section={section} />;
    case 'code':
      return <CodeRenderer section={section} />;
    case 'cta':
      return <CTARenderer section={section} />;
    case 'divider':
      return <DividerRenderer section={section} />;
    default:
      return null;
  }
};
