import ErrorPage from "@/components/common/ErrorPage";
import { useLanguage } from "@/hooks/useLanguage";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";

const InternalServerError: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  useEffect(() => {
    console.error(
      "500 Error: Internal server error occurred:",
      location.pathname
    );
  }, [location.pathname]);

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <ErrorPage
      statusCode={500}
      title={t("pages.internal_server_error.title")}
      message={t("pages.internal_server_error.message")}
      showBackButton={true}
      showHomeButton={true}
      onCustomAction={handleRefresh}
      customActionLabel={t("pages.internal_server_error.refresh")}
    />
  );
};

export default InternalServerError;
