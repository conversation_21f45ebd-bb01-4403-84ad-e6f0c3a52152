import ScoreVisualization from '@/components/analysis/ScoreVisualization';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import { toast } from '@/hooks/use-toast';
import { useLanguage } from '@/hooks/useLanguage';
import { usePageTitle } from '@/hooks/usePageTitle';
import { ReportGenerator } from '@/utils/reportGenerator';
import { LocalStorage, ResumeAnalysis } from '@/utils/storage';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useState, useEffect } from 'react';
import {
  IoArrowBack,
  IoCheckmark,
  IoChevronDown,
  IoCloudUpload,
  IoDocument,
  IoDownload,
  IoPrint,
  IoShare,
  IoWarning
} from 'react-icons/io5';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

const AnalysisResultsPage: React.FC = () => {
  const { t, currentLanguage } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();

  // Set page title (will be updated with candidate name when analysis loads)
  usePageTitle('analysis.results.title');
  const [showDownloadMenu, setShowDownloadMenu] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<ResumeAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get analysis data from location state (fallback)
  const { analysis: locationAnalysis, fileName: locationFileName } = location.state || {};

  // Load analysis data
  useEffect(() => {
    const loadAnalysis = async () => {
      setIsLoading(true);
      setError(null);

      try {
        let analysisData: ResumeAnalysis | null = null;

        // First, try to get analysis by ID from URL parameter
        if (id) {
          analysisData = LocalStorage.getAnalysisInLanguage(id, currentLanguage);

          if (!analysisData) {
            // Try to get the base analysis without language-specific data
            const allAnalyses = LocalStorage.getAllAnalyses();
            const baseAnalysis = allAnalyses.find(a => a.id === id);

            if (baseAnalysis) {
              analysisData = baseAnalysis;
            }
          }
        }

        // Fallback to location state if ID lookup fails
        if (!analysisData && locationAnalysis) {
          if (locationAnalysis.multiLanguageData && locationAnalysis.multiLanguageData[currentLanguage]) {
            analysisData = LocalStorage.getAnalysisInLanguage(locationAnalysis.id, currentLanguage) || locationAnalysis;
          } else {
            analysisData = locationAnalysis;
          }
        }

        if (analysisData) {
          setCurrentAnalysis(analysisData);
        } else {
          setError(t('analysis.results.not_found'));
        }
      } catch (err) {
        console.error('Error loading analysis:', err);
        setError(t('analysis.results.load_error'));
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalysis();
  }, [id, currentLanguage, locationAnalysis, t]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-headline mb-2">{t('analysis.results.loading')}</h2>
          <p className="text-body-text">{t('analysis.results.loading_message')}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !currentAnalysis) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <IoWarning className="w-16 h-16 text-error mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-headline mb-4">
            {error || t('analysis.results.no_data_found')}
          </h1>
          <p className="text-body-text mb-6">{t('analysis.results.no_data_message')}</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="primary"
              onClick={() => navigate('/upload')}
              leftIcon={<IoCloudUpload />}
            >
              {t('buttons.upload_resume')}
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/history')}
            >
              {t('buttons.view_all_analyses')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const analysis = currentAnalysis;
  const fileName = analysis.fileName || locationFileName || 'Unknown File';





  const handleDownloadHTML = () => {
    if (analysis) {
      ReportGenerator.downloadHTMLReport(analysis);
      setShowDownloadMenu(false);
    }
  };

  const handleDownloadJSON = () => {
    if (analysis) {
      ReportGenerator.downloadJSONReport(analysis);
      setShowDownloadMenu(false);
    }
  };

  const handlePrint = () => {
    if (analysis) {
      ReportGenerator.printReport(analysis);
      setShowDownloadMenu(false);
    }
  };

  const handleShare = async () => {
    if (!analysis) return;

    const shareData = {
      title: `${t('analysis.results.title')} - ${analysis.candidateName || t('analysis.results.anonymous_candidate')}`,
      text: `${t('analysis.results.overall_score')}: ${analysis.overallScore}% - ${analysis.summary}`,
      url: window.location.href
    };

    // Check if native sharing is supported
    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData);
        // No need for success toast as the native share dialog provides feedback
        return;
      } catch (error) {
        // User cancelled sharing or sharing failed
        if (error instanceof Error && error.name === 'AbortError') {
          // User cancelled, no need to show error
          return;
        }
        // Fall through to clipboard fallback
      }
    }

    // Fallback to copying URL to clipboard
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast({
        title: t('buttons.share'),
        description: t('notifications.link_copied'),
        variant: "default",
      });
    } catch (clipboardError) {
      // Clipboard API failed, show error
      toast({
        title: t('buttons.share'),
        description: t('notifications.share_not_supported'),
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-6xl">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-between mb-6">
            <Button
              variant="outline"
              onClick={() => navigate(-1)}
              leftIcon={<IoArrowBack />}
            >
              {t('buttons.back')}
            </Button>

            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={handleShare}
                leftIcon={<IoShare />}
              >
                {t('buttons.share')}
              </Button>

              {/* Download Dropdown */}
              <div className="relative">
                <Button
                  variant="primary"
                  onClick={() => setShowDownloadMenu(!showDownloadMenu)}
                  rightIcon={<IoChevronDown />}
                  leftIcon={<IoDownload />}
                >
                  {t('buttons.download_report')}
                </Button>

                <AnimatePresence>
                  {showDownloadMenu && (
                    <>
                      <div
                        className="fixed inset-0 z-40"
                        onClick={() => setShowDownloadMenu(false)}
                      />
                      <motion.div
                        className="absolute right-0 top-full mt-2 z-50 bg-background border border-border rounded-lg shadow-lg overflow-hidden min-w-[200px]"
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.15 }}
                      >
                        <button
                          onClick={handleDownloadHTML}
                          className="w-full text-left px-4 py-3 text-sm hover:bg-surface transition-colors flex items-center space-x-3"
                        >
                          <IoDocument className="w-4 h-4 text-primary" />
                          <div>
                            <div className="font-medium">{t('buttons.html_report')}</div>
                            <div className="text-xs text-caption">{t('analysis.results.formatted_web_page')}</div>
                          </div>
                        </button>
                        <button
                          onClick={handleDownloadJSON}
                          className="w-full text-left px-4 py-3 text-sm hover:bg-surface transition-colors flex items-center space-x-3"
                        >
                          <IoDocument className="w-4 h-4 text-accent" />
                          <div>
                            <div className="font-medium">{t('buttons.json_data')}</div>
                            <div className="text-xs text-caption">{t('buttons.raw_analysis_data')}</div>
                          </div>
                        </button>
                        <button
                          onClick={handlePrint}
                          className="w-full text-left px-4 py-3 text-sm hover:bg-surface transition-colors flex items-center space-x-3"
                        >
                          <IoPrint className="w-4 h-4 text-warning" />
                          <div>
                            <div className="font-medium">{t('buttons.print_report')}</div>
                            <div className="text-xs text-caption">{t('buttons.print_or_save_pdf')}</div>
                          </div>
                        </button>
                      </motion.div>
                    </>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
              {t('analysis.results.title')}
            </h1>
            <p className="text-xl text-body-text">
              {fileName} • {analysis.candidateName || t('analysis.results.anonymous_candidate')}
            </p>
          </div>
        </motion.div>

        {/* Overall Score */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Card variant="elevated" padding="xl" className="bg-gradient-to-br from-primary/5 to-accent/5">
            <div className="inline-flex items-center justify-center w-32 h-32 rounded-full bg-gradient-primary mb-6">
              <span className="text-4xl font-bold text-white">{analysis.overallScore}%</span>
            </div>
            <h2 className="text-2xl font-bold text-headline mb-4">
              {t('analysis.results.overall_score')}
            </h2>
            <p className="text-lg text-body-text max-w-3xl mx-auto leading-relaxed">
              {analysis.summary}
            </p>
          </Card>
        </motion.div>

        {/* Score Visualization */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <ScoreVisualization
            scores={analysis.scores}
            overallScore={analysis.overallScore}
          />
        </motion.div>

        {/* Strengths & Weaknesses */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card variant="elevated" padding="xl">
            <h3 className="text-xl font-bold text-headline mb-6 flex items-center">
              <IoCheckmark className="w-6 h-6 text-success mr-3" />
              {t('analysis.results.strengths')}
            </h3>
            <ul className="space-y-4">
              {analysis.strengths.map((strength: string, index: number) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-3 h-3 rounded-full bg-success mt-2 flex-shrink-0" />
                  <span className="text-body-text leading-relaxed">{strength}</span>
                </li>
              ))}
            </ul>
          </Card>

          <Card variant="elevated" padding="xl">
            <h3 className="text-xl font-bold text-headline mb-6 flex items-center">
              <IoWarning className="w-6 h-6 text-warning mr-3" />
              {t('analysis.results.areas_for_improvement')}
            </h3>
            <ul className="space-y-4">
              {analysis.weaknesses.map((weakness: string, index: number) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-3 h-3 rounded-full bg-warning mt-2 flex-shrink-0" />
                  <span className="text-body-text leading-relaxed">{weakness}</span>
                </li>
              ))}
            </ul>
          </Card>
        </motion.div>

        {/* Skills & Experience */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card variant="elevated" padding="xl">
            <h3 className="text-xl font-bold text-headline mb-6">
              {t('analysis.results.skills')}
            </h3>
            <div className="flex flex-wrap gap-3">
              {analysis.skills.map((skill: string, index: number) => (
                <span
                  key={index}
                  className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium"
                >
                  {skill}
                </span>
              ))}
            </div>
          </Card>

          <Card variant="elevated" padding="xl">
            <h3 className="text-xl font-bold text-headline mb-6">
              {t('analysis.results.experience')}
            </h3>
            <div className="space-y-4">
              <div>
                <span className="text-sm text-caption font-medium">{t('analysis.results.years_experience')}</span>
                <p className="text-lg font-semibold text-headline">{analysis.experience.years} years</p>
              </div>
              {analysis.experience.companies.length > 0 && (
                <div>
                  <span className="text-sm text-caption font-medium">{t('analysis.results.companies')}</span>
                  <p className="text-lg font-semibold text-headline">
                    {analysis.experience.companies.slice(0, 3).join(', ')}
                    {analysis.experience.companies.length > 3 && '...'}
                  </p>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Recommendations */}
        {analysis.recommendations.length > 0 && (
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card variant="elevated" padding="xl">
              <h3 className="text-xl font-bold text-headline mb-6">
                {t('analysis.results.recommendations')}
              </h3>
              <ul className="space-y-4">
                {analysis.recommendations.map((recommendation: string, index: number) => (
                  <li key={index} className="flex items-start space-x-4">
                    <div className="w-8 h-8 rounded-full bg-accent/10 flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-sm font-bold text-accent">{index + 1}</span>
                    </div>
                    <span className="text-body-text leading-relaxed">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </Card>
          </motion.div>
        )}

        {/* Red Flags */}
        {analysis.redFlags.length > 0 && (
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card variant="elevated" padding="xl" className="border-error/20">
              <h3 className="text-xl font-bold text-headline mb-6 flex items-center">
                <IoWarning className="w-6 h-6 text-error mr-3" />
                {t('analysis.results.red_flags')}
              </h3>
              <ul className="space-y-3">
                {analysis.redFlags.map((flag: string, index: number) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-3 h-3 rounded-full bg-error mt-2 flex-shrink-0" />
                    <span className="text-body-text leading-relaxed">{flag}</span>
                  </li>
                ))}
              </ul>
            </Card>
          </motion.div>
        )}

        {/* Actions */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="primary"
              size="lg"
              onClick={() => navigate('/upload')}
              leftIcon={<IoCloudUpload />}
            >
              {t('buttons.analyze_another')}
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigate('/history')}
            >
              {t('buttons.view_all_analyses')}
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AnalysisResultsPage;
