import LanguageSelector from "@/components/common/LanguageSelector";
import { ContentRenderer } from "@/components/content/ContentRenderer";
import { getContentForLanguage } from "@/content/contentManager";
import { useLanguage } from "@/hooks/useLanguage";
import { usePageTitle } from "@/hooks/usePageTitle";
import { SupportedLanguage } from "@/types/content";
import { motion } from "framer-motion";
import { useEffect } from "react";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { Link } from "react-router-dom";

export default function LearnMorePage() {
  const { t, currentLanguage } = useLanguage();
  usePageTitle("pages.about.meta.title");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Get content template for current language
  const contentSections = getContentForLanguage(currentLanguage as SupportedLanguage);

  return (
    <div className="min-h-screen container flex flex-col items-center justify-center bg-surface py-4">
      <header dir="ltr" className="w-full flex justify-between items-center mb-6">
        <Link to="/">
          <button
            aria-label={t("back") || "Back"}
            title={t("back") || "Back"}
            className="flex items-center justify-center"
            type="button"
          >
            <IoArrowBackCircleOutline size={32} className="text-primary" />
          </button>
        </Link>
        <LanguageSelector />
      </header>

      <main className="w-full max-w-6xl rounded-lg px-6 py-10 md:px-12 md:py-14 border border-border bg-surface/80 backdrop-blur-sm shadow-lg">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {contentSections.map((section) => (
            <ContentRenderer key={section.id} section={section} />
          ))}
        </motion.div>
      </main>
    </div>
  );
}
