import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/hooks/useLanguage';
import { usePageTitle } from '@/hooks/usePageTitle';
import DropZone from '@/components/upload/DropZone';
import ThemeAwareIllustration from '@/components/assets/ThemeAwareIllustration';

const UploadPage: React.FC = () => {
  const { t } = useLanguage();

  // Set page title
  usePageTitle('upload.meta.title');

  const handleFileProcessed = (file: File, text: string) => {
    console.log('File processed:', file.name, 'Text length:', text.length);
  };

  const handleError = (error: string) => {
    console.error('Upload error:', error);
  };



  return (
    <div className="min-h-screen py-16 ">
      <div className="container mx-auto my-auto">


          <ThemeAwareIllustration number={2} className='w-full pb-8 h-auto max-w-[200px] mx-auto'/>

        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('upload_page.title')}
          </h1>
          <p className="text-xl text-body-text max-w-2xl mx-auto">
            {t('upload_page.description')}
          </p>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <DropZone
            onFileProcessed={handleFileProcessed}
            onError={handleError}
          />
        </motion.div>


      </div>
    </div>
  );
};

export default UploadPage;
