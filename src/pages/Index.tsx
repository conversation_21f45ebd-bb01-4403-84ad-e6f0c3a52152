import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/hooks/useTheme';
import { useLanguage } from '@/hooks/useLanguage';
import Layout from '@/components/layout/Layout';
import HomePage from '@/components/pages/HomePage';
import DropZone from '@/components/upload/DropZone';
import { toast } from '@/hooks/use-toast';
import '@/i18n/config';

const Index = () => {
  const [currentView, setCurrentView] = useState('home');
  const { setTheme } = useTheme();
  const { t } = useLanguage();

  // Initialize theme and language on mount
  useEffect(() => {
    // Apply saved theme
    setTheme(document.documentElement.classList.contains('light') ? 'light' : 'dark');
  }, [setTheme]);

  const handleFileProcessed = (file: File, extractedText: string) => {
    toast({
      title: t('toast.resume_uploaded'),
      description: t('toast.extracted_characters', { count: extractedText.length, filename: file.name }),
    });
  };

  const handleError = (error: string) => {
    toast({
      title: t('notifications.upload_failed'),
      description: error,
      variant: "destructive",
    });
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'home':
        return <HomePage onGetStarted={() => setCurrentView('upload')} />;
      case 'upload':
        return (
          <div className="container mx-auto px-4 py-12 max-w-4xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center mb-8"
            >
              <h1 className="text-3xl font-bold text-headline mb-4">{t('upload_page.title')}</h1>
              <p className="text-lg text-body-text">{t('upload_page.description')}</p>
            </motion.div>
            <DropZone 
              onFileProcessed={handleFileProcessed}
              onError={handleError}
            />
          </div>
        );

      default:
        return <HomePage onGetStarted={() => setCurrentView('upload')} />;
    }
  };

  return (
    <Layout currentView={currentView} onViewChange={setCurrentView}>
      {renderCurrentView()}
    </Layout>
  );
};

export default Index;
