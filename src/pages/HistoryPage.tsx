import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLanguage } from '@/hooks/useLanguage';
import { usePageTitle } from '@/hooks/usePageTitle';
import { ReportGenerator } from '@/utils/reportGenerator';
import { LocalStorage, ResumeAnalysis } from '@/utils/storage';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useEffect, useState } from 'react';
import {
  IoAnalytics,
  IoCalendar,
  IoDocument,
  IoDownload,
  IoEllipsisVertical,
  IoEye,
  IoSearch,
  IoTrash,
  IoWarning,
} from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';

const HistoryPage: React.FC = () => {
  const { t, i18n } = useLanguage();
  const navigate = useNavigate();
  const dir = i18n?.dir ? i18n.dir() : 'ltr'; // Fallback to LTR if not present

  usePageTitle('pages.history.meta.title');
  const [analyses, setAnalyses] = useState<ResumeAnalysis[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'score' | 'name'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [showDeleteAllConfirm, setShowDeleteAllConfirm] = useState(false);

  useEffect(() => {
    loadAnalyses();
  }, []);

  const loadAnalyses = () => {
    const savedAnalyses = LocalStorage.getAllAnalyses();
    setAnalyses(savedAnalyses);
  };

  const filteredAndSortedAnalyses = analyses
    .filter(analysis =>
      analysis.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (analysis.candidateName && analysis.candidateName.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.analysisDate).getTime() - new Date(b.analysisDate).getTime();
          break;
        case 'score':
          comparison = a.overallScore - b.overallScore;
          break;
        case 'name':
          comparison = a.fileName.localeCompare(b.fileName);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const handleDelete = (id: string) => {
    LocalStorage.deleteAnalysis(id);
    loadAnalyses();
    setShowDeleteConfirm(null);
  };

  const handleDeleteAll = () => {
    LocalStorage.clearAllAnalyses();
    loadAnalyses();
    setShowDeleteAllConfirm(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-success/10 text-success border-success/20';
    if (score >= 60) return 'bg-warning/10 text-warning border-warning/20';
    return 'bg-error/10 text-error border-error/20';
  };

  return (
    <div className="min-h-screen py-8" dir={dir}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl lg:text-4xl font-bold text-headline mb-4">
            {t('pages.history.title')}
          </h1>
          <p className="text-xl text-body-text">
            {t('pages.history.description')}
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Card variant="elevated" padding="lg">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center">
                <IoDocument className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-2xl font-bold text-headline">{analyses.length}</p>
                <p className="text-sm text-caption">{t('pages.history.stats.total_analyses')}</p>
              </div>
            </div>
          </Card>
          <Card variant="elevated" padding="lg">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-success/10 flex items-center justify-center">
                <IoAnalytics className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-2xl font-bold text-headline">
                  {analyses.length > 0 ? Math.round(analyses.reduce((sum, a) => sum + a.overallScore, 0) / analyses.length) : 0}%
                </p>
                <p className="text-sm text-caption">{t('pages.history.stats.average_score')}</p>
              </div>
            </div>
          </Card>
          <Card variant="elevated" padding="lg">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-warning/10 flex items-center justify-center">
                <IoCalendar className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-2xl font-bold text-headline">
                  {analyses.filter(a => new Date(a.analysisDate) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length}
                </p>
                <p className="text-sm text-caption">{t('pages.history.stats.this_week')}</p>
              </div>
            </div>
          </Card>
          <Card variant="elevated" padding="lg">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-accent/10 flex items-center justify-center">
                <IoAnalytics className="w-6 h-6 text-accent" />
              </div>
              <div>
                <p className="text-2xl font-bold text-headline">
                  {analyses.filter(a => a.overallScore >= 80).length}
                </p>
                <p className="text-sm text-caption">{t('pages.history.stats.top_candidates')}</p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Controls Bar: full height for all items, unified alignment */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 mb-8 items-stretch"
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="relative flex-1 h-full">
            <IoSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-caption w-5 h-5" />
            <input
              type="text"
              placeholder={t('pages.history.search_placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full h-full pl-10 pr-4 py-3 rounded-lg border border-border bg-background text-body-text placeholder-caption focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
          <div className="flex items-center h-full">
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [newSortBy, newSortOrder] = value.split('-') as ['date' | 'score' | 'name', 'asc' | 'desc'];
              setSortBy(newSortBy);
              setSortOrder(newSortOrder);
            }}>
              <SelectTrigger className="w-[200px] h-full">
                <SelectValue placeholder={t('pages.history.sort.placeholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">{t('pages.history.sort.date_newest')}</SelectItem>
                <SelectItem value="date-asc">{t('pages.history.sort.date_oldest')}</SelectItem>
                <SelectItem value="score-desc">{t('pages.history.sort.score_highest')}</SelectItem>
                <SelectItem value="score-asc">{t('pages.history.sort.score_lowest')}</SelectItem>
                <SelectItem value="name-asc">{t('pages.history.sort.name_az')}</SelectItem>
                <SelectItem value="name-desc">{t('pages.history.sort.name_za')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {analyses.length > 0 && (
            <Button
              variant="destructive"
              size="md"
              onClick={() => setShowDeleteAllConfirm(true)}
              leftIcon={<IoTrash />}
              className="h-full"
            >
              {t('pages.history.actions.delete_all')}
            </Button>
          )}
        </motion.div>

        {/* Analysis List */}
        <AnimatePresence mode="wait">
          {filteredAndSortedAnalyses.length === 0 ? (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <IoDocument className="w-16 h-16 text-caption mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-headline mb-2">
                {searchTerm ? t('pages.history.empty.no_matches') : t('pages.history.empty.no_analyses')}
              </h3>
              <p className="text-body-text mb-6">
                {searchTerm ? t('pages.history.empty.adjust_search') : t('pages.history.empty.get_started')}
              </p>
              {!searchTerm && (
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => window.location.href = '/upload'}
                  leftIcon={<IoDocument />}
                >
                  {t('pages.history.actions.upload_resume')}
                </Button>
              )}
            </motion.div>
          ) : (
            <motion.div
              className="space-y-4 w-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AnimatePresence>
                {filteredAndSortedAnalyses.map((analysis) => (
                  <motion.div key={analysis.id}>
                    <Card variant="elevated" padding="lg" className="hover:shadow-lg transition-shadow">
                      {/* Card row: left info + right badge/menu */}
                      <div
                        className={`flex flex-col lg:flex-row lg:justify-between gap-4 h-full`}
                      >
                        <div className="flex-1 flex flex-col justify-between h-full">
                          <div
                            className={`flex justify-between items-start w-full ${dir === 'rtl' ? 'flex-row-reverse' : ''}`}
                          >
                            <div>
                              <h3 className="text-lg font-semibold text-headline mb-1">
                                {analysis.candidateName || t('pages.history.anonymous')}
                              </h3>
                              <p className="text-sm text-caption">
                                {analysis.fileName} • {formatFileSize(analysis.fileSize)}
                              </p>
                            </div>
                            {/* Score badge and menu next to each other, at top right/left */}
                            <div className={`flex items-center gap-2`}>
                              <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getScoreBadgeColor(analysis.overallScore)}`}>
                                {analysis.overallScore}%
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="secondary"
                                    className="px-2"
                                    aria-label={t('pages.history.actions.more')}
                                  >
                                    <IoEllipsisVertical size={18} />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align={dir === 'rtl' ? "start" : "end"}>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      navigate(`/analysis/${analysis.id}`, {
                                        state: {
                                          analysis,
                                          fileName: analysis.fileName
                                        }
                                      });
                                    }}
                                    className="flex items-center gap-2"
                                  >
                                    <IoEye size={16} />
                                    {t('pages.history.actions.view')}
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      ReportGenerator.downloadHTMLReport(analysis);
                                    }}
                                    className="flex items-center gap-2"
                                  >
                                    <IoDownload size={16} />
                                    {t('pages.history.actions.download')}
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => setShowDeleteConfirm(analysis.id)}
                                    className="flex items-center gap-2 text-error"
                                  >
                                    <IoTrash size={16} />
                                    {t('pages.history.actions.delete')}
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                          <p className="text-body-text text-sm mb-3 line-clamp-2">
                            {analysis.summary}
                          </p>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-caption">
                            <span className="flex-shrink-0">{t("pages.history.analyzed")}: {formatDate(analysis.analysisDate)}</span>
                            <span className="hidden sm:inline">•</span>
                            <div className="flex items-center gap-2 sm:gap-4">
                              <span>{t("pages.history.technical")}: {analysis.scores.technical}%</span>
                              <span>•</span>
                              <span>{t("pages.history.experience")}: {analysis.scores.experience}%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Delete Confirmation Modals */}
        <AnimatePresence>
          {showDeleteConfirm && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-background rounded-xl p-6 max-w-md w-full shadow-2xl"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <IoWarning className="w-6 h-6 text-error" />
                  <h3 className="text-lg font-semibold text-headline">{t('pages.history.delete_confirm.title')}</h3>
                </div>
                <p className="text-body-text mb-6">
                  {t('pages.history.delete_confirm.message')}
                </p>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteConfirm(null)}
                    className="flex-1"
                  >
                    {t('pages.history.delete_confirm.cancel')}
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDelete(showDeleteConfirm)}
                    className="flex-1"
                  >
                    {t('pages.history.delete_confirm.delete')}
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
          {showDeleteAllConfirm && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-background rounded-xl p-6 max-w-md w-full shadow-2xl"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <IoWarning className="w-6 h-6 text-error" />
                  <h3 className="text-lg font-semibold text-headline">{t('pages.history.delete_all_confirm.title')}</h3>
                </div>
                <p className="text-body-text mb-6">
                  {t('pages.history.delete_all_confirm.message', { count: analyses.length })}
                </p>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteAllConfirm(false)}
                    className="flex-1"
                  >
                    {t('pages.history.delete_all_confirm.cancel')}
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteAll}
                    className="flex-1"
                  >
                    {t('pages.history.delete_all_confirm.delete_all')}
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default HistoryPage;
