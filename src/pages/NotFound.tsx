import ErrorPage from "@/components/common/ErrorPage";
import { useLanguage } from "@/hooks/useLanguage";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";

const NotFound: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <ErrorPage
      statusCode={404}
      title={t("pages.not_found.title")}
      message={t("pages.not_found.message")}
      showBackButton={true}
      showHomeButton={true}
    />
  );
};

export default NotFound;
