import ErrorPage from "@/components/common/ErrorPage";
import { useLanguage } from "@/hooks/useLanguage";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";

const ServiceUnavailable: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  useEffect(() => {
    console.error(
      "503 Error: Service unavailable:",
      location.pathname
    );
  }, [location.pathname]);

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <ErrorPage
      statusCode={503}
      title={t("pages.service_unavailable.title")}
      message={t("pages.service_unavailable.message")}
      showBackButton={false}
      showHomeButton={true}
      onCustomAction={handleRefresh}
      customActionLabel={t("pages.service_unavailable.try_again")}
    />
  );
};

export default ServiceUnavailable;
