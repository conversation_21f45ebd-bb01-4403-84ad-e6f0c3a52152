import ErrorPage from "@/components/common/ErrorPage";
import { useLanguage } from "@/hooks/useLanguage";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";

const Forbidden: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  useEffect(() => {
    console.error(
      "403 Error: User attempted to access forbidden resource:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <ErrorPage
      statusCode={403}
      title={t("pages.forbidden.title")}
      message={t("pages.forbidden.message")}
      showBackButton={true}
      showHomeButton={true}
    />
  );
};

export default Forbidden;
