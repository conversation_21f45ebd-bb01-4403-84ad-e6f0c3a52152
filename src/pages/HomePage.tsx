import { useLanguage } from '@/hooks/useLanguage';
import { usePageTitle } from '@/hooks/usePageTitle';
import React from 'react';
import { useNavigate } from 'react-router-dom';

import BenefitsSection from '@/components/sections/BenefitsSection';
import CTASection from '@/components/sections/CTASection';
import FAQSection from '@/components/sections/FAQSection';
import FeaturesSection from '@/components/sections/FeaturesSection';
import HeroSection from '@/components/sections/HeroSection';
import HowItWorksSection from '@/components/sections/HowItWorksSection';

const HomePage: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Set page title
  usePageTitle('pages.home.meta.title');

  return (
    <div className="min-h-screen">
      <HeroSection t={t} navigate={navigate} />
      <FeaturesSection t={t} />
      <BenefitsSection t={t} />
      <HowItWorksSection t={t} />
      <FAQSection t={t} />
      <CTASection t={t} navigate={navigate} />
    </div>
  );
};

export default HomePage;
