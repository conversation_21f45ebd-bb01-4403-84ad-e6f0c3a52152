import { ContentTemplate } from '@/types/content';

export const spanishContent: ContentTemplate = [
  {
    id: 'hero',
    type: 'hero',
    content: {
      title: 'Bienvenido a 4CV',
      subtitle: 'Transforma tu proceso de contratación con análisis inteligente de currículums.',
      highlight: 'Revolución de Reclutamiento con IA'
    }
  },
  {
    id: 'divider-1',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'what-is-4cv',
    type: 'text',
    content: {
      heading: '¿Qué es 4CV?',
      paragraphs: [
        '4CV es una plataforma revolucionaria impulsada por IA diseñada para optimizar y mejorar tu proceso de reclutamiento. Nuestra tecnología avanzada analiza currículums con precisión sin precedentes, proporcionándote insights accionables para tomar mejores decisiones de contratación.',
        'Construido con algoritmos de aprendizaje automático de vanguardia, 4CV transforma el proceso tradicional de revisión de currículums de una tarea manual que consume tiempo en un sistema inteligente y automatizado que entrega resultados en segundos.'
      ]
    }
  },
  {
    id: 'key-features',
    type: 'list',
    content: {
      heading: 'Características Clave',
      items: [
        {
          title: 'Análisis Impulsado por IA',
          description: 'Algoritmos avanzados de aprendizaje automático que entienden contexto y matices',
          icon: '🤖'
        },
        {
          title: 'Diseño Centrado en la Privacidad',
          description: 'Todo el procesamiento ocurre localmente en tu navegador - tus datos nunca salen de tu dispositivo',
          icon: '🔒'
        },
        {
          title: 'Soporte Multi-Idioma',
          description: 'Disponible en 4 idiomas con detección inteligente de idioma',
          icon: '🌍'
        },
        {
          title: 'Procesamiento en Tiempo Real',
          description: 'Obtén resultados de análisis completos en segundos, no en horas',
          icon: '⚡'
        }
      ]
    }
  },
  {
    id: 'why-choose',
    type: 'text',
    content: {
      heading: '¿Por Qué Elegir 4CV?',
      subheading: 'Ventajas Principales',
      paragraphs: [
        'La revisión tradicional de currículums consume tiempo, es subjetiva y propensa al sesgo humano. 4CV elimina estos desafíos proporcionando análisis objetivo y completo que te ayuda a identificar a los mejores candidatos de manera rápida y justa.'
      ]
    }
  },
  {
    id: 'advantages',
    type: 'list',
    content: {
      items: [
        {
          title: 'Análisis Ultra-Rápido',
          description: 'Procesa currículums en segundos, no en horas - reduce drásticamente tu tiempo de contratación',
          icon: '🚀'
        },
        {
          title: 'Evaluación Objetiva y Sin Sesgos',
          description: 'La IA elimina el sesgo humano y proporciona evaluación consistente y justa para todos los candidatos',
          icon: '⚖️'
        },
        {
          title: 'Evaluación Integral de Habilidades',
          description: 'Analiza más de 50 criterios incluyendo habilidades técnicas, experiencia y ajuste cultural',
          icon: '📊'
        },
        {
          title: 'Protección Completa de Privacidad',
          description: 'Ningún dato sale de tu navegador - mantén control completo sobre información sensible',
          icon: '🛡️'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'testimonial',
    type: 'quote',
    content: {
      text: '¡4CV nos ayudó a reducir nuestro tiempo de selección en un 90% mientras mejoraba la calidad de los candidatos!',
      author: 'Sarah Johnson',
      role: 'Directora de RRHH',
      company: 'Startup Tecnológica'
    }
  },
  {
    id: 'divider-2',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'how-it-works',
    type: 'text',
    content: {
      heading: 'Cómo Funciona',
      subheading: 'Proceso Simple de 3 Pasos',
      paragraphs: [
        'Comenzar con 4CV es increíblemente simple. Nuestro proceso optimizado asegura que puedas empezar a analizar currículums inmediatamente sin configuración compleja o entrenamiento.'
      ]
    }
  },
  {
    id: 'process-steps',
    type: 'list',
    content: {
      items: [
        {
          title: 'Subir',
          description: 'Arrastra y suelta tu currículum PDF o selecciona desde tu dispositivo',
          icon: '📤'
        },
        {
          title: 'Analizar',
          description: 'Nuestra IA procesa el documento instantáneamente usando NLP avanzado',
          icon: '🔍'
        },
        {
          title: 'Revisar',
          description: 'Obtén insights detallados, recomendaciones y retroalimentación accionable',
          icon: '📋'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'technology',
    type: 'text',
    content: {
      heading: 'Tecnología Avanzada',
      paragraphs: [
        'Nuestra plataforma aprovecha la inteligencia artificial de vanguardia para entregar capacidades de análisis de currículums sin igual.'
      ]
    }
  },
  {
    id: 'tech-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Procesamiento de Lenguaje Natural',
          description: 'NLP avanzado para extracción precisa de habilidades y comprensión de contexto',
          icon: '🧠'
        },
        {
          title: 'Modelos de Aprendizaje Automático',
          description: 'Entrenados en miles de currículums para reconocimiento óptimo de patrones',
          icon: '🤖'
        },
        {
          title: 'Reconocimiento de Patrones',
          description: 'Evaluación inteligente de niveles de experiencia y progresión profesional',
          icon: '🔍'
        },
        {
          title: 'Análisis Contextual',
          description: 'Insights específicos del rol adaptados a tu industria y requisitos',
          icon: '🎯'
        }
      ]
    }
  },
  {
    id: 'code-example',
    type: 'code',
    content: {
      heading: 'Ejemplo de Integración',
      language: 'javascript',
      code: `// Ejemplo: Cómo 4CV procesa tu currículum
const analisis = await analyzeResume(archivoCurriculum);
console.log(analisis.habilidades, analisis.experiencia, analisis.recomendaciones);

// Obtener insights detallados
const insights = analisis.getInsights();
insights.forEach(insight => {
  console.log(\`\${insight.categoria}: \${insight.puntuacion}/100\`);
});`,
      description: 'Integración API simple para desarrolladores'
    }
  },
  {
    id: 'divider-3',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'privacy-security',
    type: 'text',
    content: {
      heading: 'Privacidad y Seguridad',
      subheading: 'Tus Datos Están Seguros',
      paragraphs: [
        'Entendemos que los datos de currículums son altamente sensibles. Por eso 4CV está construido con principios de privacidad primero, asegurando que tus datos permanezcan completamente seguros y bajo tu control.'
      ]
    }
  },
  {
    id: 'privacy-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Procesamiento Local',
          description: 'Todo sucede en tu navegador - no se envían datos a servidores externos',
          icon: '💻'
        },
        {
          title: 'Sin Almacenamiento de Datos',
          description: 'Nunca guardamos, almacenamos o cacheamos el contenido de tu currículum',
          icon: '🚫'
        },
        {
          title: 'Cumple con GDPR',
          description: 'Cumplimiento total con regulaciones internacionales de privacidad',
          icon: '✅'
        },
        {
          title: 'Código Abierto',
          description: 'Código transparente y auditable en el que puedes confiar',
          icon: '🔓'
        }
      ]
    }
  },
  {
    id: 'cta',
    type: 'cta',
    content: {
      heading: 'Comienza Hoy',
      description: '¿Listo para revolucionar tu proceso de contratación? Sube tu primer currículum y experimenta el poder del análisis impulsado por IA.',
      primaryButton: {
        text: 'Comenzar Análisis →',
        href: '/upload'
      },
      secondaryButton: {
        text: 'Ver Demo',
        href: '#demo'
      }
    }
  }
];
