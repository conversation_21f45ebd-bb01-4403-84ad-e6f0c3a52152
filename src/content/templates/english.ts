import { ContentTemplate } from '@/types/content';

export const englishContent: ContentTemplate = [
  {
    id: 'hero',
    type: 'hero',
    content: {
      title: 'Welcome to 4CV',
      subtitle: 'Transform your hiring process with intelligent resume analysis.',
      highlight: 'AI-Powered Recruitment Revolution'
    }
  },
  {
    id: 'divider-1',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'what-is-4cv',
    type: 'text',
    content: {
      heading: 'What is 4CV?',
      paragraphs: [
        '4CV is a revolutionary AI-powered platform designed to streamline and enhance your recruitment process. Our advanced technology analyzes resumes with unprecedented accuracy, providing you with actionable insights to make better hiring decisions.',
        'Built with cutting-edge machine learning algorithms, 4CV transforms the traditional resume screening process from a time-consuming manual task into an intelligent, automated system that delivers results in seconds.'
      ]
    }
  },
  {
    id: 'key-features',
    type: 'list',
    content: {
      heading: 'Key Features',
      items: [
        {
          title: 'AI-Powered Analysis',
          description: 'Advanced machine learning algorithms that understand context and nuance',
          icon: '🤖'
        },
        {
          title: 'Privacy-First Design',
          description: 'All processing happens locally in your browser - your data never leaves your device',
          icon: '🔒'
        },
        {
          title: 'Multi-Language Support',
          description: 'Available in 4 languages with intelligent language detection',
          icon: '🌍'
        },
        {
          title: 'Real-time Processing',
          description: 'Get comprehensive analysis results in seconds, not hours',
          icon: '⚡'
        }
      ]
    }
  },
  {
    id: 'why-choose',
    type: 'text',
    content: {
      heading: 'Why Choose 4CV?',
      subheading: 'Core Advantages',
      paragraphs: [
        'Traditional resume screening is time-consuming, subjective, and prone to human bias. 4CV eliminates these challenges by providing objective, comprehensive analysis that helps you identify the best candidates quickly and fairly.'
      ]
    }
  },
  {
    id: 'advantages',
    type: 'list',
    content: {
      items: [
        {
          title: 'Lightning-Fast Analysis',
          description: 'Process resumes in seconds, not hours - dramatically reduce your time-to-hire',
          icon: '🚀'
        },
        {
          title: 'Objective, Bias-Free Evaluation',
          description: 'AI removes human bias and provides consistent, fair assessment for all candidates',
          icon: '⚖️'
        },
        {
          title: 'Comprehensive Skill Assessment',
          description: 'Analyze 50+ criteria including technical skills, experience, and cultural fit',
          icon: '📊'
        },
        {
          title: 'Complete Privacy Protection',
          description: 'No data leaves your browser - maintain complete control over sensitive information',
          icon: '🛡️'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'testimonial',
    type: 'quote',
    content: {
      text: '4CV helped us reduce our screening time by 90% while improving candidate quality!',
      author: 'Sarah Johnson',
      role: 'HR Director',
      company: 'Tech Startup'
    }
  },
  {
    id: 'divider-2',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'how-it-works',
    type: 'text',
    content: {
      heading: 'How It Works',
      subheading: 'Simple 3-Step Process',
      paragraphs: [
        'Getting started with 4CV is incredibly simple. Our streamlined process ensures you can begin analyzing resumes immediately without any complex setup or training.'
      ]
    }
  },
  {
    id: 'process-steps',
    type: 'list',
    content: {
      items: [
        {
          title: 'Upload',
          description: 'Drag and drop your PDF resume or select from your device',
          icon: '📤'
        },
        {
          title: 'Analyze',
          description: 'Our AI processes the document instantly using advanced NLP',
          icon: '🔍'
        },
        {
          title: 'Review',
          description: 'Get detailed insights, recommendations, and actionable feedback',
          icon: '📋'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'technology',
    type: 'text',
    content: {
      heading: 'Advanced Technology',
      paragraphs: [
        'Our platform leverages state-of-the-art artificial intelligence to deliver unparalleled resume analysis capabilities.'
      ]
    }
  },
  {
    id: 'tech-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Natural Language Processing',
          description: 'Advanced NLP for accurate skill extraction and context understanding',
          icon: '🧠'
        },
        {
          title: 'Machine Learning Models',
          description: 'Trained on thousands of resumes for optimal pattern recognition',
          icon: '🤖'
        },
        {
          title: 'Pattern Recognition',
          description: 'Intelligent evaluation of experience levels and career progression',
          icon: '🔍'
        },
        {
          title: 'Contextual Analysis',
          description: 'Role-specific insights tailored to your industry and requirements',
          icon: '🎯'
        }
      ]
    }
  },
  {
    id: 'code-example',
    type: 'code',
    content: {
      heading: 'Integration Example',
      language: 'javascript',
      code: `// Example: How 4CV processes your resume
const analysis = await analyzeResume(resumeFile);
console.log(analysis.skills, analysis.experience, analysis.recommendations);

// Get detailed insights
const insights = analysis.getInsights();
insights.forEach(insight => {
  console.log(\`\${insight.category}: \${insight.score}/100\`);
});`,
      description: 'Simple API integration for developers'
    }
  },
  {
    id: 'divider-3',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'privacy-security',
    type: 'text',
    content: {
      heading: 'Privacy & Security',
      subheading: 'Your Data is Safe',
      paragraphs: [
        'We understand that resume data is highly sensitive. That\'s why 4CV is built with privacy-first principles, ensuring your data remains completely secure and under your control.'
      ]
    }
  },
  {
    id: 'privacy-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Local Processing',
          description: 'Everything happens in your browser - no data sent to external servers',
          icon: '💻'
        },
        {
          title: 'No Data Storage',
          description: 'We never save, store, or cache your resume content anywhere',
          icon: '🚫'
        },
        {
          title: 'GDPR Compliant',
          description: 'Full compliance with international privacy regulations',
          icon: '✅'
        },
        {
          title: 'Open Source',
          description: 'Transparent, auditable code that you can trust',
          icon: '🔓'
        }
      ]
    }
  },
  {
    id: 'cta',
    type: 'cta',
    content: {
      heading: 'Get Started Today',
      description: 'Ready to revolutionize your hiring process? Upload your first resume and experience the power of AI-driven analysis.',
      primaryButton: {
        text: 'Start Analyzing →',
        href: '/upload'
      },
      secondaryButton: {
        text: 'View Demo',
        href: '#demo'
      }
    }
  }
];
