import { ContentTemplate } from '@/types/content';

export const frenchContent: ContentTemplate = [
  {
    id: 'hero',
    type: 'hero',
    content: {
      title: 'Bienvenue sur 4CV',
      subtitle: 'Transformez votre processus de recrutement avec l\'analyse intelligente de CV.',
      highlight: 'Révolution du Recrutement par IA'
    }
  },
  {
    id: 'divider-1',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'what-is-4cv',
    type: 'text',
    content: {
      heading: 'Qu\'est-ce que 4CV ?',
      paragraphs: [
        '4CV est une plateforme révolutionnaire alimentée par l\'IA, conçue pour rationaliser et améliorer votre processus de recrutement. Notre technologie avancée analyse les CV avec une précision sans précédent, vous fournissant des insights exploitables pour prendre de meilleures décisions d\'embauche.',
        'Construit avec des algorithmes d\'apprentissage automatique de pointe, 4CV transforme le processus traditionnel de tri des CV d\'une tâche manuelle chronophage en un système intelligent et automatisé qui livre des résultats en quelques secondes.'
      ]
    }
  },
  {
    id: 'key-features',
    type: 'list',
    content: {
      heading: 'Fonctionnalités Clés',
      items: [
        {
          title: 'Analyse Alimentée par IA',
          description: 'Algorithmes d\'apprentissage automatique avancés qui comprennent le contexte et les nuances',
          icon: '🤖'
        },
        {
          title: 'Conception Axée sur la Confidentialité',
          description: 'Tout le traitement se fait localement dans votre navigateur - vos données ne quittent jamais votre appareil',
          icon: '🔒'
        },
        {
          title: 'Support Multi-Langues',
          description: 'Disponible en 4 langues avec détection intelligente de la langue',
          icon: '🌍'
        },
        {
          title: 'Traitement en Temps Réel',
          description: 'Obtenez des résultats d\'analyse complets en quelques secondes, pas en heures',
          icon: '⚡'
        }
      ]
    }
  },
  {
    id: 'why-choose',
    type: 'text',
    content: {
      heading: 'Pourquoi Choisir 4CV ?',
      subheading: 'Avantages Principaux',
      paragraphs: [
        'Le tri traditionnel des CV est chronophage, subjectif et sujet aux biais humains. 4CV élimine ces défis en fournissant une analyse objective et complète qui vous aide à identifier rapidement et équitablement les meilleurs candidats.'
      ]
    }
  },
  {
    id: 'advantages',
    type: 'list',
    content: {
      items: [
        {
          title: 'Analyse Ultra-Rapide',
          description: 'Traitez les CV en quelques secondes, pas en heures - réduisez drastiquement votre temps d\'embauche',
          icon: '🚀'
        },
        {
          title: 'Évaluation Objective et Sans Biais',
          description: 'L\'IA élimine les biais humains et fournit une évaluation cohérente et équitable pour tous les candidats',
          icon: '⚖️'
        },
        {
          title: 'Évaluation Complète des Compétences',
          description: 'Analysez plus de 50 critères incluant les compétences techniques, l\'expérience et l\'adéquation culturelle',
          icon: '📊'
        },
        {
          title: 'Protection Complète de la Confidentialité',
          description: 'Aucune donnée ne quitte votre navigateur - gardez un contrôle total sur les informations sensibles',
          icon: '🛡️'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'testimonial',
    type: 'quote',
    content: {
      text: '4CV nous a aidés à réduire notre temps de tri de 90% tout en améliorant la qualité des candidats !',
      author: 'Sarah Johnson',
      role: 'Directrice RH',
      company: 'Startup Tech'
    }
  },
  {
    id: 'divider-2',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'how-it-works',
    type: 'text',
    content: {
      heading: 'Comment Ça Marche',
      subheading: 'Processus Simple en 3 Étapes',
      paragraphs: [
        'Commencer avec 4CV est incroyablement simple. Notre processus rationalisé vous assure de pouvoir commencer à analyser les CV immédiatement sans configuration complexe ni formation.'
      ]
    }
  },
  {
    id: 'process-steps',
    type: 'list',
    content: {
      items: [
        {
          title: 'Télécharger',
          description: 'Glissez-déposez votre CV PDF ou sélectionnez depuis votre appareil',
          icon: '📤'
        },
        {
          title: 'Analyser',
          description: 'Notre IA traite le document instantanément en utilisant le NLP avancé',
          icon: '🔍'
        },
        {
          title: 'Examiner',
          description: 'Obtenez des insights détaillés, des recommandations et des commentaires exploitables',
          icon: '📋'
        }
      ],
      ordered: true
    }
  },
  {
    id: 'technology',
    type: 'text',
    content: {
      heading: 'Technologie Avancée',
      paragraphs: [
        'Notre plateforme exploite l\'intelligence artificielle de pointe pour offrir des capacités d\'analyse de CV inégalées.'
      ]
    }
  },
  {
    id: 'tech-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Traitement du Langage Naturel',
          description: 'NLP avancé pour l\'extraction précise des compétences et la compréhension du contexte',
          icon: '🧠'
        },
        {
          title: 'Modèles d\'Apprentissage Automatique',
          description: 'Entraînés sur des milliers de CV pour une reconnaissance optimale des motifs',
          icon: '🤖'
        },
        {
          title: 'Reconnaissance de Motifs',
          description: 'Évaluation intelligente des niveaux d\'expérience et de la progression de carrière',
          icon: '🔍'
        },
        {
          title: 'Analyse Contextuelle',
          description: 'Insights spécifiques au rôle adaptés à votre industrie et exigences',
          icon: '🎯'
        }
      ]
    }
  },
  {
    id: 'code-example',
    type: 'code',
    content: {
      heading: 'Exemple d\'Intégration',
      language: 'javascript',
      code: `// Exemple : Comment 4CV traite votre CV
const analyse = await analyzeResume(fichierCV);
console.log(analyse.competences, analyse.experience, analyse.recommandations);

// Obtenir des insights détaillés
const insights = analyse.getInsights();
insights.forEach(insight => {
  console.log(\`\${insight.categorie}: \${insight.score}/100\`);
});`,
      description: 'Intégration API simple pour les développeurs'
    }
  },
  {
    id: 'divider-3',
    type: 'divider',
    content: {
      style: 'line'
    }
  },
  {
    id: 'privacy-security',
    type: 'text',
    content: {
      heading: 'Confidentialité et Sécurité',
      subheading: 'Vos Données Sont Sécurisées',
      paragraphs: [
        'Nous comprenons que les données de CV sont très sensibles. C\'est pourquoi 4CV est construit avec des principes de confidentialité d\'abord, garantissant que vos données restent complètement sécurisées et sous votre contrôle.'
      ]
    }
  },
  {
    id: 'privacy-features',
    type: 'list',
    content: {
      items: [
        {
          title: 'Traitement Local',
          description: 'Tout se passe dans votre navigateur - aucune donnée envoyée vers des serveurs externes',
          icon: '💻'
        },
        {
          title: 'Aucun Stockage de Données',
          description: 'Nous ne sauvegardons, stockons ou mettons jamais en cache le contenu de votre CV',
          icon: '🚫'
        },
        {
          title: 'Conforme RGPD',
          description: 'Conformité totale avec les réglementations internationales de confidentialité',
          icon: '✅'
        },
        {
          title: 'Open Source',
          description: 'Code transparent et auditable en qui vous pouvez avoir confiance',
          icon: '🔓'
        }
      ]
    }
  },
  {
    id: 'cta',
    type: 'cta',
    content: {
      heading: 'Commencez Aujourd\'hui',
      description: 'Prêt à révolutionner votre processus d\'embauche ? Téléchargez votre premier CV et découvrez la puissance de l\'analyse pilotée par IA.',
      primaryButton: {
        text: 'Commencer l\'Analyse →',
        href: '/upload'
      },
      secondaryButton: {
        text: 'Voir la Démo',
        href: '#demo'
      }
    }
  }
];
