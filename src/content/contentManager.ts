import { LanguageContent, SupportedLanguage, ContentTemplate } from '@/types/content';
import { englishContent } from './templates/english';
import { frenchContent } from './templates/french';
import { spanishContent } from './templates/spanish';
import { arabicContent } from './templates/arabic';

export const contentTemplates: LanguageContent = {
  en: englishContent,
  fr: frenchContent,
  es: spanishContent,
  ar: arabicContent,
};

export const getContentForLanguage = (language: SupportedLanguage): ContentTemplate => {
  return contentTemplates[language] || contentTemplates.en;
};

export const getSupportedLanguages = (): SupportedLanguage[] => {
  return Object.keys(contentTemplates) as SupportedLanguage[];
};
