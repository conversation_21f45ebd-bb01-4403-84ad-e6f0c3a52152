# AI-Powered Resume Screener

A professional, modular AI-powered resume screening web application built with React, TypeScript, and modern UI libraries.

## ✨ Features

- **AI-Powered Analysis**: Intelligent resume screening using Google Gemini API
- **PDF Processing**: Client-side PDF text extraction with pdfjs-dist
- **Multi-language Support**: i18n with English, Arabic, Spanish, and French
- **Dark/Light Mode**: Persistent theme switching with system preference detection
- **Responsive Design**: Beautiful UI with Framer Motion animations
- **Local Storage**: All data processed and stored locally in browser
- **Professional Design System**: Custom CSS variables and Tailwind configuration

## 🚀 Quick Start

1. **Install dependencies**:
```bash
npm install
```

2. **Configure Gemini API** (Required for AI analysis):
   - Get your API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
   - The app will prompt you to enter it in Settings when needed
   - API key is stored securely in browser cookies

3. **Start development server**:
```bash
npm run dev
```

## 🏗️ Architecture

### Core Technologies
- **React 18** with TypeScript and functional components
- **Framer Motion** for animations and transitions
- **Tailwind CSS** with custom design system
- **react-i18next** for internationalization
- **pdfjs-dist** for client-side PDF processing
- **react-dropzone** for drag-and-drop file uploads

### File Structure
```
src/
├── components/
│   ├── common/          # Reusable UI components
│   ├── layout/          # App layout and navigation
│   ├── pages/           # Page components
│   └── upload/          # File upload components
├── hooks/               # Custom React hooks
├── i18n/               # Language files and configuration
├── utils/              # Utility functions
│   ├── storage.ts      # LocalStorage & cookies management
│   ├── pdf.ts          # PDF processing utilities
│   └── gemini.ts       # Gemini API integration
└── pages/              # Main page components
```

### Design System
- Custom CSS variables for consistent theming
- Professional color palette with semantic tokens
- Responsive animations and micro-interactions
- Support for light/dark modes with smooth transitions

## 🔧 Configuration

### API Setup
1. Navigate to Settings in the app
2. Enter your Google Gemini API key
3. Test the connection to ensure it's working

### Customization
- **Colors**: Edit `src/index.css` for color scheme
- **Languages**: Add new language files in `src/i18n/locales/`
- **Animations**: Modify `tailwind.config.ts` for custom animations

## 🌍 Internationalization

The app supports multiple languages out of the box:
- **English** (en)
- **Arabic** (ar) with RTL support
- **Spanish** (es)
- **French** (fr)

Add new languages by creating translation files in `src/i18n/locales/`.

## 🔒 Privacy & Security

- **No Backend Required**: All processing happens in the browser
- **Local Data Storage**: Resume data stored only in localStorage
- **API Key Security**: Gemini API key stored in secure cookies
- **No Data Transmission**: PDFs processed entirely client-side

## 📱 Features Overview

1. **Resume Upload**: Drag-and-drop PDF upload with validation
2. **Text Extraction**: Client-side PDF parsing and text extraction
3. **AI Analysis**: Comprehensive resume analysis via Gemini API
4. **Results Dashboard**: View and manage analysis results
5. **Export Options**: Save analyses for future reference
6. **Settings Panel**: Configure API keys and preferences

## 🛠️ Development

Built with modern development practices:
- TypeScript for type safety
- ESLint for code quality
- Modular component architecture
- Custom hooks for state management
- Utility-first CSS with Tailwind

## 📄 License

This project follows standard open-source practices.
