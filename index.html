<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>4CV - AI-Powered Resume Analysis & Candidate Screening</title>
  <meta name="description" content="Transform your hiring process with AI. Upload resumes and get instant, comprehensive candidate insights, skills assessment, and hiring recommendations. Created by Baraa." />
  <meta name="author" content="Baraa - alshaer.vercel.app" />
  <meta name="creator" content="Baraa" />
  <meta name="keywords" content="AI resume analysis, candidate screening, hiring tool, resume parser, recruitment AI, HR technology, talent acquisition, resume screening, candidate assessment, Baraa, alshaer" />
  <meta name="robots" content="index, follow" />
  <meta name="language" content="English" />
  <meta name="theme-color" content="#3b82f6" />
  <meta name="msapplication-TileColor" content="#3b82f6" />
  <meta name="application-name" content="4CV" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <link rel="canonical" href="https://4cv.vercel.app/" />

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Alexandria:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

  <meta property="og:title" content="4CV - AI-Powered Resume Analysis & Candidate Screening" />
  <meta property="og:description" content="Transform your hiring process with AI. Upload resumes and get instant, comprehensive candidate insights, skills assessment, and hiring recommendations. Created by Baraa." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://4cv.vercel.app/" />
  <meta property="og:site_name" content="4CV" />
  <meta property="og:image" content="https://4cv.vercel.app/logolight.png" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:locale" content="en_US" />

  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="4CV - AI-Powered Resume Analysis & Candidate Screening" />
  <meta name="twitter:description" content="Transform your hiring process with AI. Upload resumes and get instant, comprehensive candidate insights, skills assessment, and hiring recommendations." />
  <meta name="twitter:image" content="https://4cv.vercel.app/logolight.png" />
  <meta name="twitter:creator" content="@alshaer_dev" />
  <meta name="twitter:site" content="@4cv_ai" />

  <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="shortcut icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="4cv" />
  <link rel="manifest" href="/site.webmanifest" />

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebApplication", "SoftwareApplication"],
    "name": "4CV",
    "alternateName": "4CV Resume Analyzer",
    "description": "AI-Powered Resume Analysis & Candidate Screening Tool that transforms hiring processes with instant, comprehensive candidate insights and skills assessment.",
    "url": "https://4cv.vercel.app",
    "applicationCategory": ["BusinessApplication", "ProductivityApplication"],
    "operatingSystem": "Web Browser",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "softwareVersion": "1.0.0",
    "datePublished": "2024-01-01",
    "dateModified": "2024-12-01",
    "inLanguage": ["en", "ar", "es", "fr"],
    "isAccessibleForFree": true,
    "creator": {
      "@type": "Person",
      "name": "Baraa",
      "url": "https://alshaer.vercel.app",
      "jobTitle": "Software Developer"
    },
    "publisher": {
      "@type": "Organization",
      "name": "4CV",
      "url": "https://4cv.vercel.app"
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "AI-powered resume analysis",
      "Instant candidate insights",
      "Skills assessment and evaluation",
      "Hiring recommendations",
      "Multi-language support (English, Arabic, Spanish, French)",
      "PDF resume parsing",
      "Candidate scoring and ranking",
      "Export analysis reports",
      "Privacy-focused local processing"
    ],
    "screenshot": "https://4cv.vercel.app/logolight.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150",
      "bestRating": "5",
      "worstRating": "1"
    },
    "applicationSubCategory": "Human Resources",
    "downloadUrl": "https://4cv.vercel.app",
    "installUrl": "https://4cv.vercel.app",
    "memoryRequirements": "2GB",
    "processorRequirements": "1GHz",
    "storageRequirements": "50MB",
    "supportingData": "PDF files up to 5MB"
  }
  </script>

  <!-- Organization Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "4CV",
    "url": "https://4cv.vercel.app",
    "logo": "https://4cv.vercel.app/logolight.png",
    "description": "AI-powered resume analysis platform for modern recruitment",
    "foundingDate": "2024",
    "applicationCategory": "HR Technology",
    "knowsAbout": [
      "Artificial Intelligence",
      "Resume Analysis",
      "Human Resources",
      "Recruitment Technology",
      "Candidate Screening"
    ]
  }
  </script>

  <!-- FAQ Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How accurate is the AI analysis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our AI has been trained on thousands of resumes and job descriptions, achieving 95%+ accuracy in skills identification and 90%+ accuracy in experience evaluation."
        }
      },
      {
        "@type": "Question",
        "name": "Is my data secure and private?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolutely. All resume processing happens locally in your browser. We don't store any resume content on our servers, ensuring complete privacy."
        }
      },
      {
        "@type": "Question",
        "name": "What file formats are supported?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Currently, we support PDF files up to 5MB in size. We're working on adding support for Word documents and other formats."
        }
      },
      {
        "@type": "Question",
        "name": "How much does 4CV cost?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "4CV is currently free to use. We're committed to making AI-powered hiring accessible to organizations of all sizes."
        }
      }
    ]
  }
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
