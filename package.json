{"name": "4cv", "private": true, "version": "1.0.0", "description": "AI-Powered Resume Analysis & Candidate Screening Tool", "author": "<PERSON><PERSON> <<EMAIL>> (https://alshaer.vercel.app)", "homepage": "https://4cv.vercel.app", "repository": {"type": "git", "url": "https://github.com/baraa-alshaer/4cv.git"}, "keywords": ["ai", "resume-analysis", "candidate-screening", "hiring-tool", "recruitment", "hr-technology", "react", "typescript", "vite"], "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "npm run copy-pdf-worker && vite build", "build:dev": "npm run copy-pdf-worker && vite build --mode development", "build:analyze": "npm run copy-pdf-worker && vite build && npx vite-bundle-analyzer dist", "lint": "eslint .", "preview": "vite preview", "copy-pdf-worker": "cp node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/pdf.worker.min.js", "postinstall": "npm run copy-pdf-worker"}, "dependencies": {"@404pagez/react": "^0.1.1", "@headlessui/react": "^2.2.7", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.56.2", "@tanstack/react-virtual": "^3.13.12", "@theatre/core": "^0.7.2", "@theatre/studio": "^0.7.2", "@theme-toggles/react": "^4.1.0", "@types/js-cookie": "^3.0.6", "@uidotdev/usehooks": "^2.4.1", "@use-gesture/react": "^10.3.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "i18next": "^25.3.2", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "lottie-react": "^2.4.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "pdfjs-dist": "^5.4.54", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-country-flag": "^3.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-hotkeys-hook": "^5.1.0", "react-i18next": "^15.6.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.1", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "web-vitals": "^5.1.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-bundle-analyzer": "^1.1.0"}}